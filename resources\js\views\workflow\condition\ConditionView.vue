<template>
    <CCol :xs="12">
        <div class="form-container" :class="{ 'form-disabled': isDisabled }">
            <Form ref="form" :class="{ 'pointer-events-none': isDisabled }">
                <CCol :xs="12" class="mb-3">
                    <label class="mb-1">
                        {{ $t('workflow.condition.name') }}
                        <span class="text-danger">*</span>
                    </label>
                    <Field 
                        v-model="state.conditionDetail.name"
                        name="name" 
                        type="text" 
                        class="form-control" 
                        maxlength="200" 
                    />
                    <ErrorMessage
                        as="div"
                        name="name"
                        class="text-danger"
                    />
                </CCol>
                <div v-for="(condition, index) in state.conditionDetail.arrayConditions" :key="index">
                    <b-dropdown variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab mb-3" v-if="index > 0">
                        <template #button-content>
                            <span class="text-secondary cursor-pointer bg-select-logical-operator">{{ getLogicalOperatorLabel(condition.logicalOperator) }}</span>
                        </template>
                        <b-dropdown-item
                            v-for="(optionLogicalOperator, indexLogicalOperator) in state.selectOptionLogicalOperators"
                            :key="indexLogicalOperator"
                            @click="selectLogicalOperatorEdit(index, optionLogicalOperator.value)"
                        >
                            {{ optionLogicalOperator.label }}
                        </b-dropdown-item>
                    </b-dropdown>
                    <div class="filter-container mb-3 border">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label required-label">
                                    {{ $t('workflow.condition.object') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="selection-field">
                                    <Field
                                        :name="`conditions[${index}].object`"
                                        as="select"
                                        class="form-select"
                                        v-model="condition.object"
                                        @change="handleChangeObjectEdit(index)"
                                    >
                                        <option v-for="(optionObject, indexOptionObject) in state.selectOptionObjects" :value="optionObject.value" :key="indexOptionObject">
                                            {{ optionObject.label }}
                                        </option>
                                    </Field>
                                    <ErrorMessage
                                        as="div"
                                        :name="`conditions[${index}].object`"
                                        class="text-danger"
                                    />
                                </div>
                            </div>
                            <div class="col-md-7">
                                <label class="form-label required-label">
                                    {{ $t('workflow.condition.add') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="condition-container">
                                    <div class="mb-2">
                                        <i>{{ $t('workflow.condition.and') }}</i>
                                    </div>
                                    <div v-for="(conditionSub, subIndex) in state.conditionDetail.arrayConditionSubs[index]" :key="subIndex">
                                        <div class="d-flex align-items-center">
                                            <span class="field-label">{{ conditionSub.field.display_name }}</span>
                                            <b-dropdown variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab dropdown-operator">
                                                <template #button-content>
                                                    <span class="ms-3 text-secondary cursor-pointer">{{ getOperatorLabel(conditionSub) }}</span>
                                                </template>
                                                <b-dropdown-item 
                                                    v-for="(optionOperator, indexOperator) in conditionSub.filteredOperators"
                                                    :key="indexOperator" 
                                                    @click="selectOperatorEdit(index, subIndex, optionOperator)"
                                                >
                                                    {{ optionOperator.label }}
                                                </b-dropdown-item>
                                            </b-dropdown>
                                        </div>
                                        <div class="date-input-container d-flex align-items-center">
                                            <input 
                                                type="text" 
                                                class="form-control"
                                                v-model="conditionSub.value"
                                                v-if="checkInputTypeText(conditionSub.field.type)" 
                                            >
                                            <div class="d-flex align-items-center" v-if="conditionSub.operator.value === 'between' && checkInputTypeNumber(conditionSub.field.type)">
                                                <input 
                                                    type="number" 
                                                    class="form-control me-4"
                                                    v-model="conditionSub.value[0]" 
                                                >
                                                <input 
                                                    type="number" 
                                                    class="form-control"
                                                    v-model="conditionSub.value[1]" 
                                                >
                                            </div>
                                            <input 
                                                type="number" 
                                                class="form-control" 
                                                v-model="conditionSub.value"
                                                :disabled="conditionSub.disableInput"
                                                v-if="conditionSub.operator.value !== 'between' && checkInputTypeNumber(conditionSub.field.type)"
                                            >
                                            <input 
                                                type="time" 
                                                class="form-control" 
                                                v-model="conditionSub.value"
                                                v-if="checkInputTypeTime(conditionSub.field.type)" 
                                            >
                                            <div class="d-flex align-items-center" v-if="conditionSub.operator.value === 'between' && checkInputTypeDate(conditionSub.field.type)">
                                                <input 
                                                    type="date" 
                                                    class="form-control me-4"
                                                    v-model="conditionSub.value[0]" 
                                                >
                                                <input 
                                                    type="date" 
                                                    class="form-control"
                                                    v-model="conditionSub.value[1]" 
                                                >
                                            </div>
                                            <input 
                                                type="date" 
                                                class="form-control" 
                                                v-model="conditionSub.value"
                                                :disabled="conditionSub.disableInput"
                                                v-if="conditionSub.operator.value !== 'between' && checkInputTypeDate(conditionSub.field.type)"
                                            >
                                            <Multiselect
                                                v-model="conditionSub.value"
                                                :mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                :placeholder="$t('workflow.choose')"
                                                :close-on-select="false"
                                                :searchable="true"
                                                :options="conditionSub.field.options"
                                                :can-clear="false"
                                                v-if="checkInputTypeSelect(conditionSub.field.type)"
                                            />
                                            <Multiselect
                                                v-model="conditionSub.value"
                                                :mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                :placeholder="$t('workflow.choose')"
                                                :close-on-select="false"
                                                :searchable="true"
                                                :object="true"
                                                :options="state.selectOptionDepartments"
                                                :can-clear="false"
                                                v-if="checkInputTypeDepartment(conditionSub.field.type)"
                                            />
                                            <Multiselect
                                                v-model="conditionSub.value"
                                                :mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                :placeholder="$t('workflow.choose')"
                                                :close-on-select="false"
                                                :filter-results="false"
                                                :resolve-on-load="false"
                                                :infinite="true"
                                                :limit="20"
                                                :clear-on-search="true"
                                                :searchable="true"
                                                :delay="0"
                                                :min-chars="0"
                                                :object="true"
                                                :options="async (query) => {
                                                    return await debouncedGetOptionUsers(query)
                                                }"
                                                :can-clear="false"
                                                @open="debouncedGetOptionUsers('')"
                                                v-if="checkInputTypeUser(conditionSub.field.type)"
                                            />
                                            <Multiselect
                                                v-model="conditionSub.value"
                                                :mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                :placeholder="$t('workflow.choose')"
                                                :close-on-select="false"
                                                :filter-results="false"
                                                :resolve-on-load="false"
                                                :infinite="true"
                                                :limit="20"
                                                :clear-on-search="true"
                                                :searchable="true"
                                                :delay="0"
                                                :min-chars="0"
                                                :object="true"
                                                :options="async (query) => {
                                                    return await debouncedGetOptionColumnData(query, conditionSub.field)
                                                }"
                                                :can-clear="false"
                                                @open="debouncedGetOptionColumnData('', conditionSub.field)"
                                                v-if="checkInputTypeObjectSystem(conditionSub.field.type)"
                                            />
                                            <Multiselect
                                                v-model="conditionSub.value"
                                                mode="tags"
                                                :placeholder="$t('workflow.choose')"
                                                :close-on-select="false"
                                                :searchable="true"
                                                :object="true"
                                                :options="state.selectOptionDepartments"
                                                :can-clear="false"
                                                v-if="checkInputTypeDepartmentSystem(conditionSub.field.type)"
                                            >
                                                <template v-slot:option="{ option }">
                                                    <div class="custom-option">
                                                        <div class="option-label mb-1">
                                                            {{ option.label }}
                                                        </div>
                                                        <div class="option-description text-secondary">
                                                            <small>
                                                                <i>{{ option.type }}</i>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </template>
                                            </Multiselect>
                                            <Multiselect
                                                v-model="conditionSub.value"
                                                mode="tags"
                                                :placeholder="$t('workflow.choose')"
                                                :close-on-select="false"
                                                :searchable="true"
                                                :object="true"
                                                :options="state.selectOptionJobPositionSystems"
                                                :can-clear="false"
                                                v-if="checkInputTypeJobPositionSystem(conditionSub.field.type)"
                                            />
                                            <Multiselect
                                                v-model="conditionSub.value"
                                                mode="tags"
                                                :placeholder="$t('workflow.choose')"
                                                :close-on-select="false"
                                                :searchable="true"
                                                :object="true"
                                                :options="state.selectOptionRanks"
                                                :can-clear="false"
                                                v-if="checkInputTypeRank(conditionSub.field.type)"
                                            />
                                            <Multiselect
                                                v-model="conditionSub.value"
                                                mode="tags"
                                                :placeholder="$t('workflow.choose')"
                                                :close-on-select="false"
                                                :filter-results="false"
                                                :resolve-on-load="false"
                                                :infinite="true"
                                                :limit="20"
                                                :clear-on-search="true"
                                                :searchable="true"
                                                :delay="0"
                                                :min-chars="0"
                                                :object="true"
                                                :options="async (query) => {
                                                    return await debouncedGetOptionUsers(query)
                                                }"
                                                :can-clear="false"
                                                @open="debouncedGetOptionUsers('')"
                                                v-if="checkInputTypeCreateBy(conditionSub.field.type, conditionSub.field.object)"
                                            />
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="24px"
                                                viewBox="0 -960 960 960"
                                                width="24px"
                                                fill="#83868C"
                                                class="cursor-pointer ms-2 float-end"
                                                @click="removeConditionSubEdit(index, subIndex)" 
                                            >
                                                <path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    height="24px"
                                    viewBox="0 -960 960 960"
                                    width="24px"
                                    fill="#83868C"
                                    class="cursor-pointer ms-2"
                                    @click="removeConditionEdit(index)" 
                                >
                                    <path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </Form>
            <div v-if="isDisabled" class="form-overlay"></div>
        </div>
    </CCol>
    <Loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted, computed } from 'vue'
import { useToast } from 'vue-toast-notification';
import { useI18n } from "vue-i18n";
import { Form, Field, ErrorMessage } from 'vee-validate';
import Multiselect from '@vueform/multiselect';
import useOptions from '@/composables/option';
import useFields from '@/composables/field';
import debounce from 'lodash.debounce';
import { WORKFLOWS } from "@/constants/constants";
import Loading from '@/views/loading/Loading.vue'

export default defineComponent({
    name: 'ConditionAdd',
    emits: ['close-modal-condition', 'reset-modal-condition', 'add-condition', 'edit-condition', 'remove-condition'],

    components: {
        Multiselect,
        Form,
		Field,
		ErrorMessage,
        Loading
    },

    props: {
        dataConditions: {
            type: Object,
            default: {},
            required: true,
        },
        fieldCreateds: {
            type: Array as () => Array<any>,
            required: true,
            default: () => []
        },
        isDisabled: {
            type: Boolean,
            default: false
        },
    },

    setup(props: any, {emit}) {
        const { t }  = useI18n();
        const $toast = useToast();

        const state = reactive({
            indexEdit: null as any,
            conditionDetail: {} as any,
            selectOptionObjects: [
				{ label: t('workflow.choose'), value: '' },
				{ label: t('workflow.object.form'), value: WORKFLOWS.CONDITION.OPTION_OBJECT.FORM },
				{ label: t('workflow.object.process'), value: WORKFLOWS.CONDITION.OPTION_OBJECT.PROCESS },
			] as Array<any>,
            selectOptionOperators: [
                { label: t('workflow.operator.equal'), value: 'equal' },
                { label: t('workflow.operator.not_equal'), value: 'not_equal' },
                { label: t('workflow.operator.in'), value: 'in' },
                { label: t('workflow.operator.not_in'), value: 'not_in' },
                { label: t('workflow.operator.greater'), value: 'greater' },
                { label: t('workflow.operator.lower'), value: 'lower' },
                { label: t('workflow.operator.not_greater'), value: 'not_greater' },
                { label: t('workflow.operator.not_lower'), value: 'not_lower' },
                { label: t('workflow.operator.contain'), value: 'contain' },
                { label: t('workflow.operator.not_contain'), value: 'not_contain' },
                { label: t('workflow.operator.empty'), value: 'empty' },
                { label: t('workflow.operator.not_empty'), value: 'not_empty' },
                { label: t('workflow.operator.between'), value: 'between' },
                { label: t('workflow.operator.other'), value: 'other' },
			] as Array<any>,
            selectOptionSystems: [
                { display_name: t('workflow.option_condition_system.department'), keyword: "department_ids", type: 'DEPARTMENT_SYSTEM' }, 
                { display_name: t('workflow.option_condition_system.job_position'), keyword: "job_position_ids", type: 'JOB_POSITION_SYSTEM' },
                { display_name: t('workflow.option_condition_system.rank'), keyword: "rank_ids", type: 'RANK_SYSTEM' },
			] as Array<any>,
            selectOptionProcesss: [
                { display_name: t('workflow.option_processs.name'), keyword: "name", type: "VARCHAR", object: WORKFLOWS.CONDITION.OPTION_OBJECT.PROCESS },
                { display_name: t('workflow.option_processs.description'), keyword: "description", type: "VARCHAR", object: WORKFLOWS.CONDITION.OPTION_OBJECT.PROCESS },
                { display_name: t('workflow.option_processs.create_by'), keyword: "create_by", type: "CREATE_BY", object: WORKFLOWS.CONDITION.OPTION_OBJECT.PROCESS },
			] as Array<any>,
            selectOptionLogicalOperators: [
                { label: t('workflow.condition.operator_and'), value: WORKFLOWS.CONDITION.OPERATOR.AND },
                { label: t('workflow.condition.operator_or'), value: WORKFLOWS.CONDITION.OPERATOR.OR },
			] as Array<any>,
            selectOptionFields: [] as Array<any>,
            searchQueryField: '',
            selectOptionDepartments: [] as Array<any>,
            selectOptionJobPositionSystems: [] as Array<any>,
            selectOptionRanks: [] as Array<any>,
        });

        onMounted( async () => {
            if (props.dataConditions.length > 0) {
                await formatConditionDataForEdit(props.dataConditions[0]);
            }
		});

        const filteredOptionFields = computed(() => {
            return state.selectOptionFields.filter(field => 
                field.display_name.toLowerCase().includes(state.searchQueryField.toLowerCase())
            );
        })

        // Hàm format dữ liệu từ API format sang component format
        const formatConditionDataForEdit = async (condition: any) => {
            state.conditionDetail = condition;
            // Kiểm tra nếu không có orConditions thì format từ or_conditions
            if ('id' in condition) {
                if (condition.formatted === undefined) {
                    const orConditions = condition.or_conditions;
                    const arrayConditions: any[] = [];
                    const arrayConditionSubs: any[][] = [];

                    // Load options cho các field hệ thống nếu cần
                    await getOptionDepartments();
                    await getOptionJobPositionSystems();
                    await getOptionRanks();

                    // Xử lý từng orCondition với async/await
                    for (const orCondition of orConditions) {
                        // Tạo arrayConditions
                        arrayConditions.push({
                            object: orCondition.object || '',
                            logicalOperator: orCondition.logicalOperator || 'and'
                        });

                        // Tạo arrayConditionSubs từ filter.ors
                        const conditionSubs: any[] = [];
                        if (orCondition.filter && orCondition.filter.ors) {
                            for (const filterItem of orCondition.filter.ors) {
                                // Tìm field từ fieldSetups và fieldCreateds
                                let field: any = null;

                                // Tìm trong fieldSetups và fieldCreateds nếu object là 'form'
                                if (orCondition.object === 'form') {
                                    const combinedFields = getCombinedFields();
                                    field = combinedFields.find((f: any) => f.keyword === filterItem.f);
                                } else if (orCondition.object === 'process') {
                                    // Tìm trong selectOptionProcesss hoặc selectOptionSystems
                                    field = state.selectOptionProcesss.find((f: any) => f.keyword === filterItem.f) || state.selectOptionSystems.find((f: any) => f.keyword === filterItem.f);
                                }

                                // Tìm operator
                                const operator = state.selectOptionOperators.find((op: any) => op.value === filterItem.o) || state.selectOptionOperators[0];

                                // Lọc operators theo type
                                const filteredOperators = filterOperatorsByType(field.type);

                                // Convert value từ array IDs sang array objects cho Multiselect
                                let formattedValue = filterItem.p;
                                if (Array.isArray(filterItem.p) && field.type) {
                                    if (field.type === 'RANK_SYSTEM') {
                                        formattedValue = filterItem.p.map((id: string) => {
                                            const option = state.selectOptionRanks.find((rank: any) => rank.value === id);
                                            return option || { value: id, label: id };
                                        });
                                    } else if (field.type === 'DEPARTMENT_SYSTEM') {
                                        formattedValue = filterItem.p.map((id: string) => {
                                            const option = state.selectOptionDepartments.find((dept: any) => dept.value === id);
                                            return option || { value: id, label: id };
                                        });
                                    } else if (field.type === 'JOB_POSITION_SYSTEM') {
                                        formattedValue = filterItem.p.map((id: string) => {
                                            const option = state.selectOptionJobPositionSystems.find((pos: any) => pos.value === id);
                                            return option || { value: id, label: id };
                                        });
                                    } else if (field.type === 'CREATE_BY') {
                                        // Với CREATE_BY, cần load user data để convert ID thành object
                                        const userPromises = filterItem.p.map(async (id: string) => {
                                            try {
                                                // Load user data bằng cách search theo ID
                                                const users = await getOptionUsers('');
                                                const user: any = users?.find((u: any) => u.value === id);
                                                return user || { value: id, label: `${user.account_name} - ${user.full_name}` };
                                            } catch (error) {
                                                return { value: id, label: `User ID: ${id}` };
                                            }
                                        });
                                        formattedValue = await Promise.all(userPromises);
                                    }
                                }

                                conditionSubs.push({
                                    field: field,
                                    operator: operator,
                                    filteredOperators: filteredOperators,
                                    value: formattedValue
                                });
                            }
                        }

                        arrayConditionSubs.push(conditionSubs);
                    }

                    // Thêm các thuộc tính đã format vào condition
                    condition.orConditions = orConditions;
                    condition.arrayConditions = arrayConditions;
                    condition.arrayConditionSubs = arrayConditionSubs;
                }
            }
            condition.formatted = true;
            
            return condition;
        };

        const removeConditionEdit = (index: number): void => {
            state.conditionDetail.arrayConditions.splice(index, 1);
            state.conditionDetail.arrayConditionSubs.splice(index, 1);
        }

        const removeConditionSubEdit = (index: number, subIndex: number): void => {
            if (state.conditionDetail.arrayConditionSubs[index]) {
                state.conditionDetail.arrayConditionSubs[index].splice(subIndex, 1);
            }
        }

        const filterOperatorsByType = (value: string) => {
            const operatorValues = {
                VARCHAR: ['contain', 'not_contain'],
                TEXT: ['contain', 'not_contain'],
                INTEGER: ['equal', 'greater', 'not_lower', 'lower', 'not_greater', 'between', 'empty', 'not_empty', 'other'],
                FLOAT: ['equal', 'greater', 'not_lower', 'lower', 'not_greater', 'between', 'empty', 'not_empty', 'other'],
                DATE: ['equal', 'greater', 'not_lower', 'lower', 'not_greater', 'between', 'empty', 'not_empty', 'other'], 
                RADIO: ['in', 'not_in', 'empty', 'not_empty'],
                TIME: ['equal', 'greater', 'not_lower', 'lower', 'not_greater', 'empty', 'not_empty', 'other'],
                SELECT: ['in', 'not_in', 'empty', 'not_empty'],
                CHECKLIST: ['in', 'not_in', 'empty', 'not_empty'],
                USER: ['in', 'not_in', 'empty', 'not_empty'],
                DEPARTMENT: ['in', 'not_in', 'empty', 'not_empty'],
                FORMULA: ['equal', 'greater', 'not_lower', 'lower', 'not_greater', 'between', 'empty', 'not_empty', 'other'],
                OBJECTSYSTEM: ['in', 'not_in', 'empty', 'not_empty'],
                DEPARTMENT_SYSTEM: ['in', 'not_in', 'empty', 'not_empty'],
                JOB_POSITION_SYSTEM: ['in', 'not_in', 'empty', 'not_empty'],
                RANK_SYSTEM: ['in', 'not_in', 'empty', 'not_empty'],
                CREATE_BY: ['in', 'not_in', 'empty', 'not_empty'],
            };

            const allowedValues = operatorValues[value] || [];

            return state.selectOptionOperators.filter(op => allowedValues.includes(op.value));
        }

        const selectLogicalOperatorEdit = (index: number, logicalOperator: string) => {
            if (state.conditionDetail.arrayConditions[index]) {
                state.conditionDetail.arrayConditions[index].logicalOperator = logicalOperator;
            }
        }

        const selectOperatorEdit = (index: number, subIndex: number, operator: any) => {
            if (state.conditionDetail.arrayConditionSubs[index] && state.conditionDetail.arrayConditionSubs[index][subIndex]) {
                const conditionSub = state.conditionDetail.arrayConditionSubs[index][subIndex];
                conditionSub.operator = operator;
                // console.log(operator);
                // Nếu toán tử là 'empty' hoặc 'not_empty', xóa giá trị và vô hiệu hóa nhập liệu
                if (operator.value === 'empty' || operator.value === 'not_empty') {
                    conditionSub.value = null; // Hoặc '', tùy thuộc vào yêu cầu
                    conditionSub.disableInput = true; // Thêm thuộc tính để vô hiệu hóa trường nhập
                } else {
                    conditionSub.disableInput = false; // Cho phép nhập lại nếu toán tử khác
                }
                if (operator.value === 'between') {
                    // Khởi tạo giá trị là mảng [null, null] để lưu hai giá trị
                    conditionSub.value = [null, null];
                } else {
                    // Nếu không phải 'between', đặt giá trị về null
                    conditionSub.value = null;
                }
            }
        }

        const getOperatorLabel = (conditionSub: any) => {
            return conditionSub.operator ? conditionSub.operator.label : (conditionSub.filteredOperators[0] ? conditionSub.filteredOperators[0].label : '');
        }

        const getLogicalOperatorLabel = (logicalOperator: string) => {
            return logicalOperator == WORKFLOWS.CONDITION.OPERATOR.AND ? t('workflow.condition.operator_and') : t('workflow.condition.operator_or');
        }
        
        const checkInputTypeText = (type: string) => {
            if (type == 'VARCHAR' || type == 'TEXT' || type == 'FORMULA') {
                return true;
            }
        }

        const checkInputTypeNumber = (type: string) => {
            if (type == 'INTEGER' || type == 'FLOAT') {
                return true;
            }
        }

        const checkInputTypeTime = (type: string) => {
            if (type == 'TIME') {
                return true;
            }
        }

        const checkInputTypeDate = (type: string) => {
            if (type == 'DATE') {
                return true;
            }
        }

        const checkInputTypeSelect = (type: string) => {
            if (type == 'RADIO' || type == 'CHECKLIST' || type == 'SELECT') {
                return true;
            }
        }

        const checkInputTypeUser = (type: string) => {
            if (type == 'USER') {
                return true;
            }
        }

        const checkInputTypeDepartment = (type: string) => {
            if (type == 'DEPARTMENT') {
                return true;
            }
        }

        const checkInputTypeObjectSystem = (type: string) => {
            if (type == 'OBJECTSYSTEM') {
                return true;
            }
        }

        const checkInputTypeDepartmentSystem = (type: string) => {
            if (type == 'DEPARTMENT_SYSTEM') {
                return true;
            }
        }

        const checkInputTypeJobPositionSystem = (type: string) => {
            if (type == 'JOB_POSITION_SYSTEM') {
                return true;
            }
        }

        const checkInputTypeRank = (type: string) => {
            if (type == 'RANK_SYSTEM') {
                return true;
            }
        }

        const checkInputTypeCreateBy = (type: string, object: string) => {
            if (type == 'CREATE_BY' && object == WORKFLOWS.CONDITION.OPTION_OBJECT.PROCESS) {
                return true;
            }
        }

        const { getUsers, getDepartments, getJobPositionSystems, getRankSystems, setIsLoading } = useOptions();
        const { getColumnDatas } = useFields();

        // Helper function để kết hợp fieldSetups và fieldCreateds
        const getCombinedFields = () => {
            return [...props.fieldCreateds];
        };

        const getOptionDepartments = async () => {
            let result = await getDepartments();
            if (Array.isArray(result) && result.length > 0) {
                state.selectOptionDepartments = result.map((elem: any) => (
					{
						value: elem.id,
						label: elem.name,
                        type: elem.type,
					} 
				));
            }
		}

        const getOptionJobPositionSystems = async () => {
            let result = await getJobPositionSystems();
            if (Array.isArray(result) && result.length > 0) {
                state.selectOptionJobPositionSystems = result.map((elem: any) => (
					{
						value: elem.id,
						label: elem.name,
					} 
				));
            }
		}

        const getOptionRanks = async () => {
            let result = await getRankSystems();
            if (Array.isArray(result) && result.length > 0) {
                state.selectOptionRanks = result.map((elem: any) => (
					{
						value: elem.id,
						label: elem.name,
					} 
				));
            }
		}

        const getOptionUsers = async (query: string) => {
			let result = await getUsers(query);
			if (Array.isArray(result) && result.length > 0) {
                return result.map((elem: any) => (
                    {
                        value: elem.id,
                        label: `${elem.account_name} - ${elem.full_name}`,
                    } 
                ));
            }
		}

		const debouncedGetOptionUsers = debounce(getOptionUsers, 500);

        const getOptionColumnData = async (query: string, field: any) => {
			let result = await getColumnDatas(field.object_table, field.sub_column_table, field.column_table, query);
			if (!!result && Array.isArray(result.data_options) && result.data_options.length > 0) {
                return result.data_options.map((elem: any) => (
					{
						value: elem.id,
						label: elem[field.column_table],
					} 
				));
            }
		}

		const debouncedGetOptionColumnData = debounce(getOptionColumnData, 500);

        const handleChangeObjectEdit = (index: number) => {
            state.conditionDetail.arrayConditionSubs[index] = [];
        }

        return {
            setIsLoading,
            state,
            filteredOptionFields,
            selectLogicalOperatorEdit,
            removeConditionEdit,
            removeConditionSubEdit,
            selectOperatorEdit,
            getOperatorLabel,
            getLogicalOperatorLabel,
            checkInputTypeText,
            checkInputTypeNumber,
            checkInputTypeTime,
            checkInputTypeDate,
            checkInputTypeSelect,
            checkInputTypeUser,
            checkInputTypeDepartment,
            checkInputTypeObjectSystem,
            checkInputTypeDepartmentSystem,
            checkInputTypeJobPositionSystem,
            checkInputTypeRank,
            checkInputTypeCreateBy,
            debouncedGetOptionUsers,
            debouncedGetOptionColumnData,
            handleChangeObjectEdit,
            getCombinedFields,
        }
    },
});
</script>
<style type="text/css" scoped>
svg {
    cursor: pointer;
}
.table__td--action {
    min-width: 70px !important;
}
.condition-container {
    background-color: #f7f8f9;
    border-radius: 0.175rem;
    padding: 15px;
    position: relative;
}
.bg-add-condition {
    background-color: #eee;
    border-radius: 0.175rem;
    padding: 5px;
}
.cursor-pointer {
    cursor: pointer;
}
.filter-container {
    padding: 15px;
}
.form-select {
    line-height: 1.875;
}
.dropdown-operator {
    margin-top: 0px !important;
}
.bg-select-logical-operator {
    background-color: #eee;
    border-radius: 0.175rem;
    padding-top: 3px;
    padding-right: 10px;
    padding-bottom: 3px;
    padding-left: 10px;
}

/* Form disabled styles */
.form-container {
    position: relative;
}

.form-disabled {
    opacity: 0.6;
    pointer-events: none;
}

.form-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.3);
    z-index: 10;
    cursor: not-allowed;
    backdrop-filter: blur(0.5px);
}

.pointer-events-none {
    pointer-events: none;
}

/* Disabled state for form elements */
.form-disabled input,
.form-disabled select,
.form-disabled .form-control,
.form-disabled .form-select,
.form-disabled .multiselect,
.form-disabled .dropdown,
.form-disabled svg {
    pointer-events: none;
    cursor: not-allowed !important;
}

.form-disabled .multiselect {
    opacity: 0.85;
}

/* Not allowed cursor for all elements when disabled */
.form-disabled *,
.form-overlay * {
    cursor: not-allowed !important;
}

/* Hover effects for disabled state */
.form-disabled:hover,
.form-overlay:hover {
    cursor: not-allowed !important;
}

/* Additional not-allowed styling */
.form-disabled .cursor-pointer {
    cursor: not-allowed !important;
}

.form-disabled .dropdown-toggle,
.form-disabled .btn,
.form-disabled button {
    cursor: not-allowed !important;
}

/* Ensure all interactive elements show not-allowed cursor */
.form-disabled .b-dropdown,
.form-disabled .dropdown-menu-tab {
    cursor: not-allowed !important;
}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>