<?php

namespace App\Services;

use App\Models\EmailTemplate;
use App\Jobs\SendPreparedEmailJob;
use Illuminate\Support\Facades\Storage;
use App\Repositories\User\UserRepositoryInterface;

class EmailService
{
    protected $conditionService;
    protected $userRepository;

    public function __construct(
        ProcessConditionService $conditionService,
        UserRepositoryInterface $userRepository
    ) {
        $this->conditionService = $conditionService;
        $this->userRepository = $userRepository;
    }

    /**
     * Lấy danh sách các template email cần gửi
     */
    public function getEmailInStage($field_values, $processInstance, $currentStageId, $actionId, $processVersion, $user, $emailConditionIds)
    {
        $stageEmailConfigs = $processVersion->stageEmailConfigs->where('stage_id', $currentStageId)->where('action_id', $actionId);
        $templateIds = [];
        
        if ($stageEmailConfigs->isNotEmpty()) {
            foreach ($stageEmailConfigs as $stageEmailConfig) {
                // L<PERSON>y các điều kiện liên quan đến StageEmailConfig
                $emailConditions = $stageEmailConfig->emailConditions;
                
                if ($emailConditions->isNotEmpty()) {
                    // Lưu trữ hoặc xử lý các điều kiện
                    $emailConditionIds = is_string($emailConditionIds) ? json_decode($emailConditionIds, true) : $emailConditionIds;
                    foreach ($emailConditions as $emailCondition) {
                        if (is_null($emailConditionIds) || in_array($emailCondition->condition_id, $emailConditionIds)) {
                            $condition = $emailCondition->condition;
                            $or_conditions = $condition->or_conditions;
                            $checkCondition = $this->conditionService->checkCondition($field_values, $processInstance, $or_conditions, $user);
                            $normalizedCheckCondition = is_bool($checkCondition) ? var_export($checkCondition, true) : strtolower($checkCondition);
                            $normalizedConditionStatus = is_bool($emailCondition->condition_status) ? var_export($emailCondition->condition_status, true) : strtolower($emailCondition->condition_status);

                            if ($normalizedCheckCondition === $normalizedConditionStatus) {
                                $templateIds[] = $stageEmailConfig->template_id;
                            }
                        }
                    }
                } else {
                    // Nếu không có điều kiện, mặc định thêm `template_id`
                    $templateIds[] = $stageEmailConfig->template_id;
                }
            }
            
            // Loại bỏ các giá trị trùng lặp và trả về kết quả
            $templateIds = array_unique($templateIds);
            return $templateIds;
        }
        
        return [];
    }

    /**
     * Gửi email theo các template ID
     */
    public function sendEmail($field_values, $emailStageIds, $job_id, $stageIds)
    {
        // --- 1. Xử lý gửi email ---
        foreach ($emailStageIds as $emailId) {
            // Lấy thông tin EmailTemplate từ DB
            $emailTemplate = EmailTemplate::find($emailId);

            if (!$emailTemplate) {
                continue; // Bỏ qua template ID không hợp lệ
            }
            
            $replacementData = array_filter($field_values, fn($value) => $value !== null);

            // Tạo URL chi tiết công việc
            $jobDetailUrl = config('app.url') . '/job/' . $job_id;

            // --- Gọi phương thức nội bộ để chuẩn bị và dispatch Job ---
            $dispatched = $this->prepareAndDispatchEmailJob($emailTemplate, $replacementData, $job_id, $stageIds, $jobDetailUrl);
            
            return true;
        }
        
        return false;
    }

    /**
     * Làm phẳng dữ liệu email
     */
    public function flattenEmailData(array $emailsData): array
    {
        $flat = [];
        array_walk_recursive($emailsData, function($value, $key) use (&$flat) {
            if ($key === 'email') {
                $flat[] = $value;
            }
        });
        return $flat;
    }

    /**
     * Kiểm tra xem kết quả có được nhóm không
     */
    public function isGroupedResult(array $data): bool
    {
        if (empty($data)) {
            return false;
        }
        if (!isset($data[0]) || !is_array($data[0])) {
            return false;
        }
        // Kiểm tra key của phần tử đầu tiên có phải là các số liên tiếp không
        return array_keys($data[0]) === range(0, count($data[0]) - 1);
    }

    /**
     * Chuẩn bị và gửi email job
     */
    public function prepareAndDispatchEmailJob($emailTemplate, $replacementData, $job_id, $stageIds, $jobDetailUrl = null): bool
    {
        $templateId = $emailTemplate->id;
        try {
            // --- 1. Phân giải Người nhận (To, CC, BCC) ---
            $toEmails = $this->userRepository->getUserByOptionScopeRes($emailTemplate->to_emails ?? [], $job_id, $stageIds);
            $ccEmails = $this->userRepository->getUserByOptionScopeRes($emailTemplate->cc_emails ?? [], $job_id, $stageIds);
            $bccEmails = $this->userRepository->getUserByOptionScopeRes($emailTemplate->bcc_emails ?? [], $job_id, $stageIds);
            
            if (empty($toEmails)) {
                return false;
            }
            
            // --- 2. Chuẩn bị Đường dẫn File đính kèm ---
            $attachmentPaths = $this->prepareAttachmentPaths($emailTemplate->files ?? null, 'file_upload_emails');

            // --- 3. Xác định địa chỉ và tên "From" ---
            $fromAddress = null;
            $fromName = null;
            $fromEmailSetting = $emailTemplate->from_email ?? null;
            
            if ($fromEmailSetting) {
                if (preg_match('/^(.*)<(.+)>$/', $fromEmailSetting, $matches)) {
                    $namePart = trim($matches[1]);
                    $emailPart = trim($matches[2]);
                    if (filter_var($emailPart, FILTER_VALIDATE_EMAIL)) {
                        $fromAddress = $emailPart;
                        $fromName = !empty($namePart) ? $namePart : null;
                    }
                } elseif (filter_var($fromEmailSetting, FILTER_VALIDATE_EMAIL)) {
                    $fromAddress = $fromEmailSetting;
                }
            }

            // --- 4. Lấy Template Thô cho Tiêu đề và Nội dung ---
            $subjectTemplate = $emailTemplate->name_title;
            $contentTemplate = $emailTemplate->content;
            
            // --- 5. Dispatch Job vào Hàng đợi ---
            // Xử lý toEmails: nếu grouped thì gửi riêng từng group,
            // còn nếu flat thì gửi chung.
            // dd($toEmails);
            if ($this->isGroupedResult($toEmails)) {
                // Nếu toEmails là grouped, lặp qua các nhóm (dựa vào index)
                foreach ($toEmails as $index => $group) {
                    $toList = array_column($group, 'email');
                    // Nếu CC được trả về dạng grouped, lấy theo chỉ số của nhóm, nếu không (flat) thì dùng flatten toàn bộ.
                    if ($this->isGroupedResult($ccEmails)) {
                        $ccList = isset($ccEmails[$index])
                            ? array_column($ccEmails[$index], 'email')
                            : [];
                    } else {
                        $ccList =  $this->flattenEmailData($ccEmails);
                    }
                    // Tương tự cho BCC.
                    if ($this->isGroupedResult($bccEmails)) {
                        $bccList = isset($bccEmails[$index])
                            ? array_column($bccEmails[$index], 'email')
                            : [];
                    } else {
                        $bccList = $this->flattenEmailData($bccEmails);
                    }
    
                    SendPreparedEmailJob::dispatch(
                        $toList,
                        $ccList,
                        $bccList,
                        $subjectTemplate,
                        $contentTemplate,
                        $replacementData,
                        $attachmentPaths,
                        $fromAddress,
                        $fromName,
                        $templateId,
                        $jobDetailUrl
                    )->onQueue('emails');
                }
            } else {
                // Nếu toEmails là flat, xử lý như bình thường.
                $toList = array_column($toEmails, 'email');
                // Kiểm tra ccEmails: nếu grouped thì flatten, nếu không thì dùng array_column
                $ccList = $this->isGroupedResult($ccEmails) 
                    ? $this->flattenEmailData($ccEmails) 
                    : array_column($ccEmails, 'email');
                // Tương tự cho BCC
                $bccList = $this->isGroupedResult($bccEmails) 
                    ? $this->flattenEmailData($bccEmails) 
                    : array_column($bccEmails, 'email'); 
    
                SendPreparedEmailJob::dispatch(
                    $toList,
                    $ccList,
                    $bccList,
                    $subjectTemplate,
                    $contentTemplate,
                    $replacementData,
                    $attachmentPaths,
                    $fromAddress,
                    $fromName,
                    $templateId,
                    $jobDetailUrl
                )->onQueue('emails');
            }
            
            return true;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Chuẩn bị đường dẫn tệp đính kèm
     */
    protected function prepareAttachmentPaths(mixed $filenames, string $storageSubPath): array
    {
        if (!is_array($filenames)) return [];

        $paths = [];
        // Sử dụng disk 'local' (mặc định trỏ đến storage/app)
        $disk = Storage::disk('local');
        $baseRelativePath = trim($storageSubPath, '/'); // $baseRelativePath sẽ là 'file_upload_emails'
        
        foreach ($filenames as $filename) {
            if (!is_string($filename) || empty(trim($filename))) continue;
            $filename = trim($filename);
            // Tạo đường dẫn tương đối của file trong disk 'local'
            $relativePath = !empty($baseRelativePath) ? $baseRelativePath . '/' . $filename : $filename;
            if ($disk->exists($relativePath)) {
                $paths[] = $disk->path($relativePath); // Lấy đường dẫn tuyệt đối từ disk 'local'
            }
        }
        
        return $paths;
    }
} 