import { reactive, computed } from 'vue'
import useDynamicFormLogic from '@/composables/useDynamicFormLogic'
import { numberCommas } from '@/utils/utils'

export default function useDynamicFormState() {
	const { 
		converFormFields, 
		initializeItemChildrens,
		calculateFormulaWithFormData,
		sumColumn
	} = useDynamicFormLogic()

	// Core dynamic form state
	const createDynamicFormState = () => reactive({
		formData: {} as any,
		itemChildrens: {} as { [key: string]: any[] },
		dataFormFields: [] as Array<any>,
		selectOptionDepartments: [] as Array<any>,
		subColumnTableDescription: {} as { [key: string]: any },
		subColumnTableOptionSelected: {} as { [key: string]: any },
		subColumnTableDescriptionChildren: {} as { [key: string]: { [index: number]: any } },
		subColumnTableOptionSelectedChildren: {} as { [key: string]: { [index: number]: any } },
		maxFiles: 50,
		maxFileSize: '5MB',
		acceptedFileTypes: [
			'image/*',
			'application/pdf', 
			'application/msword', 
			'application/vnd.ms-excel', 
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'text/plain' 
		]
	})

	// Sorted form fields computed
	const useSortedFormFields = (state: any) => {
		return computed(() => {
			const transformedFields = converFormFields(state.dataFormFields, false);
			initializeItemChildrens(transformedFields, state.itemChildrens);
			return transformedFields;
		});
	}

	// Formula calculations
	const useFormulaCalculations = (sortedFormFields: any, formData: any, itemChildrens: any) => {
		const formulaResults = computed(() => {
			const results = {};
			sortedFormFields.value.forEach((field: any) => {
				if (field.type === 'FORMULA') {
					const formula = field.value;
					if (formula.startsWith('=')) {
						results[field.name] = calculateFormulaWithFormData(field.value, results, formData);
					} else {
						results[field.name] = sumColumn(field, itemChildrens);
					}
				}
			});
			return results;
		});

		const formattedFormulaResults = computed(() => {
			console.log(54);
			
			const results = {};
			for (const key in formulaResults.value) {
				results[key] = numberCommas(formulaResults.value[key]);
				formData[key] = results[key];
			}
			return results;
		});

		return { formulaResults, formattedFormulaResults };
	}

	return {
		createDynamicFormState,
		useSortedFormFields,
		useFormulaCalculations
	}
}
