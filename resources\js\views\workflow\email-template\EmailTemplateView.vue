<template>
    <CCol :xs="12">
        <div class="form-container" :class="{ 'form-disabled': isDisabled }">
            <Form ref="form" :class="{ 'pointer-events-none': isDisabled }">
                <CCol :xs="12" class="mb-3">
                    <label class="mb-1">
                        {{ $t('workflow.email.name') }}
                        <span class="text-danger">*</span>
                    </label>
                    <Field 
                        v-model="state.emailTemplateDetail.name"
                        name="name" 
                        type="text" 
                        class="form-control" 
                        maxlength="100" 
                    />
                    <ErrorMessage
                        as="div"
                        name="name"
                        class="text-danger"
                    />
                </CCol>
                <CCol :xs="12" class="mb-3">
                    <label class="mb-1">
                        {{ $t('workflow.email.condition') }}
                    </label>
                    <Field
                        v-model="state.emailTemplateDetail.condition"
                        name="condition"
                        type="text"
                        class="form-control"
                        maxlength="100"
                    />
                </CCol>
                <CCol :xs="12" class="mb-3">
                    <label class="mb-1">
                        {{ $t('workflow.email.from_email') }}
                        <span class="text-danger">*</span>
                    </label>
                    <Multiselect
                        :placeholder="$t('workflow.choose')"
                        :close-on-select="false"
                        :searchable="true"
                        :object="true"
                        :can-clear="false"
                        v-model="state.emailTemplateDetail.from_email"
                    />
                </CCol>
                <CCol :xs="12" class="mb-3">
                    <label class="mb-1">
                        {{ $t('workflow.email.name_title') }}
                        <span class="text-danger">*</span>
                    </label>
                    <Field 
                        name="name_title" 
                        type="text" 
                        class="form-control" 
                        maxlength="200" 
                        v-model="state.emailTemplateDetail.name_title"
                        @focus="isDropdownVisible = true" 
                        @blur="handleBlurDropdownHide"
                    />
                    <ErrorMessage
                        as="div"
                        name="name_title"
                        class="text-danger"
                    />
                    <div class="dropdown" v-if="isDropdownVisible">
                        <BFormInput 
                            class="form-control mt-2" 
                            :placeholder="$t('search.title')" 
                            v-model="state.searchQueryField" 
                            @focus="isDropdownVisible = true" 
                            @blur="handleBlurDropdownHide"
                        />
                        <ul class="list-group mt-2">
                            <template v-if="filteredOptionFields.length > 0">
                                <li 
                                    class="list-group-item" 
                                    v-for="(field, indexField) in filteredOptionFields" 
                                    :key="indexField" 
                                    @click="addOptionFieldEdit(field)"
                                >
                                    <p class="text-dark">{{ field.display_name }}</p>
                                    <p class="text-secondary"><i>&lbrace;{{ field.keyword }}&rbrace;</i></p>
                                </li>
                            </template>
                            <template v-else>
                                <li class="list-group-item">{{ $t('search.no_matching_records_found') }}</li>
                            </template>
                        </ul>
                    </div>
                </CCol>
                <CCol :xs="12" class="mb-3">
                    <label class="mb-1">
                        {{ $t('workflow.email.to_emails') }}
                        <span class="text-danger">*</span>
                    </label>
                    <div class="d-flex align-items-center">
                        <Field 
                            name="to_emails"
                            v-slot="{ field }"
                        >
                            <Multiselect
                                mode="tags"
                                v-bind="field"
                                v-model="state.emailTemplateDetail.to_emails"
                                :placeholder="$t('workflow.choose')"
                                :close-on-select="false"
                                :filter-results="false"
                                :resolve-on-load="false"
                                :infinite="true"
                                :limit="20"
                                :clear-on-search="true"
                                :searchable="true"
                                :delay="0"
                                :min-chars="0"
                                :object="true"
                                :options="async (query) => {
                                    return await debouncedGetOptionScopes(query)
                                }"
                                @open="debouncedGetOptionScopes('')"
                                :can-clear="false"
                            >
                                <template v-slot:option="{ option }">
                                    <div class="custom-option">
                                        <div class="option-label mb-1">
                                            {{ option.label }}
                                        </div>
                                        <div class="option-description text-secondary">
                                            <small>
                                                <i>{{ option.description }}</i>
                                            </small>
                                        </div>
                                    </div>
                                </template>
                            </Multiselect>
                        </Field>
                        <span class="ms-2 cursor-pointer" @click="toggleEmailCc">{{ $t('workflow.email.cc_emails') }}</span> 
                        <span class="ms-2 cursor-pointer" @click="toggleEmailBcc">{{ $t('workflow.email.bcc_emails') }}</span> 
                    </div>
                    <ErrorMessage
                        as="div"
                        name="to_emails"
                        class="text-danger"
                    />
                </CCol>
                <CCol :xs="12" class="mb-3" v-if="state.activeEmailCc || state.emailTemplateDetail.cc_emails &&state.emailTemplateDetail.cc_emails.length > 0">
                    <label class="mb-1">
                        {{ $t('workflow.email.cc_emails') }}
                    </label>
                    <Multiselect
                        mode="tags"
                        v-model="state.emailTemplateDetail.cc_emails"
                        :placeholder="$t('workflow.choose')"
                        :close-on-select="false"
                        :filter-results="false"
                        :resolve-on-load="false"
                        :infinite="true"
                        :limit="20"
                        :clear-on-search="true"
                        :searchable="true"
                        :delay="0"
                        :min-chars="0"
                        :object="true"
                        :options="async (query) => {
                            return await debouncedGetOptionScopes(query)
                        }"
                        @open="debouncedGetOptionScopes('')"
                        :can-clear="false"
                    >
                        <template v-slot:option="{ option }">
                            <div class="custom-option">
                                <div class="option-label mb-1">
                                    {{ option.label }}
                                </div>
                                <div class="option-description text-secondary">
                                    <small>
                                        <i>{{ option.description }}</i>
                                    </small>
                                </div>
                            </div>
                        </template>
                    </Multiselect>
                </CCol>
                <CCol :xs="12" class="mb-3" v-if="state.activeEmailBcc || state.emailTemplateDetail.bcc_emails && state.emailTemplateDetail.bcc_emails.length > 0">
                    <label class="mb-1">
                        {{ $t('workflow.email.bcc_emails') }}
                    </label>
                    <Multiselect
                        mode="tags"
                        v-model="state.emailTemplateDetail.bcc_emails"
                        :placeholder="$t('workflow.choose')"
                        :close-on-select="false"
                        :filter-results="false"
                        :resolve-on-load="false"
                        :infinite="true"
                        :limit="20"
                        :clear-on-search="true"
                        :searchable="true"
                        :delay="0"
                        :min-chars="0"
                        :object="true"
                        :options="async (query) => {
                            return await debouncedGetOptionScopes(query)
                        }"
                        @open="debouncedGetOptionScopes('')"
                        :can-clear="false"
                    >
                        <template v-slot:option="{ option }">
                            <div class="custom-option">
                                <div class="option-label mb-1">
                                    {{ option.label }}
                                </div>
                                <div class="option-description text-secondary">
                                    <small>
                                        <i>{{ option.description }}</i>
                                    </small>
                                </div>
                            </div>
                        </template>
                    </Multiselect>
                </CCol>
                <CCol :xs="12" class="mb-3">
                    <label class="mb-1">
                        {{ $t('workflow.email.content') }}
                        <span class="text-danger">*</span>
                    </label>
                    <Field 
                        v-model="state.emailTemplateDetail.content"
                        name="content"
                        as="textarea"
                        class="form-control" 
                        :maxlength="20000" 
                        rows="5"
                        @focus="isContentDropdownVisible = true" 
                        @blur="handleBlurContentDropdownHide"
                    />
                    <ErrorMessage
                        as="div"
                        name="content"
                        class="text-danger"
                    />
                    <div class="dropdown" v-if="isContentDropdownVisible">
                        <BFormInput 
                            class="form-control mt-2" 
                            :placeholder="$t('search.title')" 
                            v-model="state.searchContentQueryField" 
                            @focus="isContentDropdownVisible = true" 
                            @blur="handleBlurContentDropdownHide"
                        />
                        <ul class="list-group mt-2">
                            <template v-if="filteredContentOptionFields.length > 0">
                                <li 
                                    class="list-group-item" 
                                    v-for="(field, indexField) in filteredContentOptionFields" 
                                    :key="indexField" 
                                    @click="addContentOptionFieldEdit(field)"
                                >
                                    <p class="text-dark">{{ field.display_name }}</p>
                                    <p class="text-secondary"><i>&lbrace;{{ field.keyword }}&rbrace;</i></p>
                                </li>
                            </template>
                            <template v-else>
                                <li class="list-group-item">{{ $t('search.no_matching_records_found') }}</li>
                            </template>
                        </ul>
                    </div>
                </CCol>
                <CCol :xs="12" class="mb-3">
                    <label class="mb-1">
                        {{ $t('workflow.email.files') }}
                    </label>
                    <FilePond
                        :files="state.emailTemplateDetail.files"
                        @updatefiles="(fileItemUploads) => updateFileEdits(fileItemUploads)"
                        className="file-pond"
                        :labelIdle="$t('validate_field.file_upload.label_idle')"
                        :allowMultiple="true"
                        :maxFiles="state.maxFiles"
                        :maxFileSize="state.maxFileSize"
                        :acceptedFileTypes="state.acceptedFileTypes"
                        :labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
                        :labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
                        :fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
                        :labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
                        :instantUpload="false"
                        name="files"
                        ref="files"
                        credits="false"
                        allow-reorder="true"
                        item-insert-location="after"
                        image-preview-min-height="60"
                        image-preview-max-height="60"
                    />
                </CCol>
            </Form>
        </div>
    </CCol>
    <loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import { useToast } from 'vue-toast-notification';
import { useI18n } from "vue-i18n";
import Multiselect from '@vueform/multiselect';
import Loading from '@/views/loading/Loading.vue'
import { Form, Field, ErrorMessage } from 'vee-validate';
import debounce from 'lodash.debounce';
import useOptions from '@/composables/option';
import { WORKFLOWS } from "@/constants/constants";
import vueFilePond from 'vue-filepond';
import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';

const FilePond: any = vueFilePond(FilePondPluginImagePreview, FilePondPluginFileValidateType, FilePondPluginFileValidateSize);

export default defineComponent({
    name: 'EmailTemplateAdd',
    emits: ['close-modal-email-template', 'reset-modal-email-template', 'add-email-template', 'edit-email-template', 'remove-email-template'],

    components: {
        Multiselect,
        Loading,
        Form,
		Field,
		ErrorMessage,
        FilePond
    },

    props: {
        dataEmailTemplate: {
            type: Object,
            default: {},
            required: true,
        },
        fieldCreateds: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
        isDisabled: {
            type: Boolean,
            default: false
        },
    },

    setup(props: any, {emit}) {
        const { t }  = useI18n();

        const isDropdownVisible = ref(false);
        const isContentDropdownVisible = ref(false);

        const state = reactive({
			maxFiles: 50,
			maxFileSize: '5MB',
			acceptedFileTypes: [
				'image/*',
				'application/pdf', 
				'application/msword', 
				'application/vnd.ms-excel', 
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				'text/plain' 
			],
            tabIndex: 0,
            indexEdit: null as any,
            emailTemplateDetail: {} as any,
            activeEmailCc: false,
            activeEmailBcc: false,
            selectOptionFields: [] as Array<any>,
            searchQueryField: '',
            searchContentQueryField: '',
            selectOptionFromEmails: [
				{ label: '<EMAIL>', description: `${t('workflow.option_from_email.email_desc')}`, value: '<EMAIL>' },
			] as Array<any>,
            selectOptionSystemDefaults: [
				{ label: `${t('workflow.option_system_default.create_by')}`, description: `${t('workflow.option_system_default.create_by_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.CREATE_BY_ID },
                { label: `${t('workflow.option_system_default.user')}`, description: `${t('workflow.option_system_default.user_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.USER_ID },
                { label: `${t('workflow.option_system_default.approve_by')}`, description: `${t('workflow.option_system_default.approve_by_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.APPROVE_BY_ID },
			] as Array<any>,
		});

        const updateFileEdits = (fileItemUploads: any) => {
			state.emailTemplateDetail.files = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem : any) => fileItem.file) : null;
		};

        onMounted( async () => {
            if (props.dataEmailTemplate.length > 0) {
                let emailTemplate = await formatApiDataToFormData(props.dataEmailTemplate[0]);
                
                state.emailTemplateDetail = emailTemplate;
            }
		}); 

        const formatApiDataToFormData = async (apiData: any) => {
            // Find from_email option from selectOptionFromEmails
            const fromEmailOption = state.selectOptionFromEmails.find((option: any) =>
                option.value === apiData.from_email
            );

            // Format to_emails array
            const formatEmailArray = async (emailArray: string[] | null) => {
                if (!emailArray) return [];

                // Get all available options from getOptionProcessScopes
                const dataOptionProcessScope = await getOptionProcessScopes('');
                const systemOption = dataOptionProcessScope.filter((item: any) =>
                    emailArray.includes(item.value)
                );

                return emailArray.map((email: string) => {
                    const foundOption = systemOption.find((option: any) =>
                        option.value === email
                    );
                    return foundOption || { label: email, value: email };
                });
            };

            return {
                id: apiData.id,
                name: apiData.name,
                condition: apiData.condition && apiData.condition[0] ? apiData.condition[0].name : '',
                from_email: fromEmailOption ? (fromEmailOption.label ? fromEmailOption : { label: apiData.from_email, value: apiData.from_email }) : apiData.from_email,
                name_title: apiData.name_title,
                to_emails: Array.isArray(apiData.to_emails) && apiData.to_emails.some((email: any) => email.label)
                    ? apiData.to_emails
                    : await formatEmailArray(apiData.to_emails),
                cc_emails: Array.isArray(apiData.cc_emails) && apiData.cc_emails.some((email: any) => email.label)
                    ? apiData.cc_emails
                    : await formatEmailArray(apiData.cc_emails),
                bcc_emails: Array.isArray(apiData.bcc_emails) && apiData.bcc_emails.some((email: any) => email.label)
                    ? apiData.bcc_emails
                    : await formatEmailArray(apiData.bcc_emails),
                content: apiData.content,
                files: apiData.files ? formatFilesForFilePond(apiData.files) : [],
            };
        }

        const formatFilesForFilePond = (files: any[]) => {
            return files.map((file: any) => {
                if (typeof file._relativePath === 'undefined') {
                    return {
                        source: typeof file === 'object' ? file.name : file,
                        options: {
                            type: 'local',
                            file: {
                                name: typeof file === 'object' ? file.name : file,
                                size: 0,
                            },
                        }
                    };
                }
                return file;
            });
        }

        const toggleEmailCc = () => {
            state.activeEmailCc = !state.activeEmailCc;
        }

        const toggleEmailBcc = () => {
            state.activeEmailBcc = !state.activeEmailBcc;
        }
        
        const filteredOptionFields = computed(() => {
            // Lấy tất cả các key trong children của các item có type là TABLE
            const tableChildKeys = getCombinedFields()
                .filter((item: any) => item.type === 'TABLE' && item.childrens)
                .flatMap((item: any) => item.childrens.map((child: any) => child.value));
            // Lọc các item không có type là table hoặc file và không có key nằm trong tableKeys
            state.selectOptionFields = getCombinedFields().filter((item: any) => {
                if (item.type === 'TABLE' || item.type === 'FILEUPLOAD') {
                    return false; // Loại bỏ item có type là TABLE hoặc FILEUPLOAD
                }

                if (tableChildKeys.includes(item.keyword)) {
                    return false; // Loại bỏ item có keyword nằm trong tableKeys
                }

                return true; // Giữ lại các item khác
            }); 

            return state.selectOptionFields.filter(field => 
                field.display_name.toLowerCase().includes(state.searchQueryField.toLowerCase())
            );
        });

        const filteredContentOptionFields = computed(() => {
            // Lấy tất cả các key trong children của các item có type là TABLE
            const tableChildKeys = getCombinedFields()
                .filter((item: any) => item.type === 'TABLE' && item.childrens)
                .flatMap((item: any) => item.childrens.map((child: any) => child.value));
            // Lọc các item không có type là table hoặc file và không có key nằm trong tableKeys
            const selectOptionFields = getCombinedFields().filter((item: any) => {
                if (item.type === 'TABLE' || item.type === 'FILEUPLOAD') {
                    return false; // Loại bỏ item có type là TABLE hoặc FILEUPLOAD
                }

                if (tableChildKeys.includes(item.keyword)) {
                    return false; // Loại bỏ item có keyword nằm trong tableKeys
                }

                return true; // Giữ lại các item khác
            }); 

            return selectOptionFields.filter(field => 
                field.display_name.toLowerCase().includes(state.searchContentQueryField.toLowerCase())
            );
        });

        const handleBlurDropdownHide = (event: any) => {
            state.searchQueryField = ''; 
            // Kiểm tra xem người dùng có nhấp vào một tùy chọn hay không
            setTimeout(() => {
                if (!event.relatedTarget || (!event.relatedTarget.classList.contains('list-group-item') && !event.relatedTarget.classList.contains('form-control'))) {
                    isDropdownVisible.value = false; // Ẩn dropdown khi mất tiêu điểm
                }
            }, 300); // Thêm một chút thời gian để cho phép sự kiện click xảy ra
        };

        const addOptionFieldEdit = (optionField: any) => {
            // Thêm tùy chọn vào ô input hiện tại mà không có dấu phẩy
            state.emailTemplateDetail.name_title += (state.emailTemplateDetail.name_title ? ' ' : '') + `{${optionField.keyword}}`; // Thêm một khoảng trắng nếu ô không rỗng
            state.searchQueryField = ''; 
            isDropdownVisible.value = false;
        };

        const handleBlurContentDropdownHide = (event: any) => {
            state.searchContentQueryField = ''; 
            // Kiểm tra xem người dùng có nhấp vào một tùy chọn hay không
            setTimeout(() => {
                if (!event.relatedTarget || (!event.relatedTarget.classList.contains('list-group-item') && !event.relatedTarget.classList.contains('form-control'))) {
                    isContentDropdownVisible.value = false; // Ẩn dropdown khi mất tiêu điểm
                }
            }, 300); // Thêm một chút thời gian để cho phép sự kiện click xảy ra
        };

        const addContentOptionFieldEdit = (optionField: any) => {
            // Thêm tùy chọn vào ô input hiện tại mà không có dấu phẩy
            state.emailTemplateDetail.content += (state.emailTemplateDetail.content ? ' ' : '') + `{${optionField.keyword}}`; // Thêm một khoảng trắng nếu ô không rỗng
            state.searchContentQueryField = ''; 
            isContentDropdownVisible.value = false;
        };

        const { getScopes, setIsLoading } = useOptions();

        const getCombinedFields = () => {
            return [...props.fieldCreateds];
        };

        const getOptionProcessScopes = async (query: string) => {
            let result = await getScopes(query);
            if (Array.isArray(result) && result.length > 0) {
                // Kết hợp các option mặc định với kết quả trả về từ API`
                return [...state.selectOptionSystemDefaults, ...result];
            }

            // Nếu không có kết quả từ API, chỉ trả về các option mặc định
            return [...state.selectOptionSystemDefaults];
		}

		const debouncedGetOptionScopes = debounce(getOptionProcessScopes, 500);

        return {
            setIsLoading,
            state,
            updateFileEdits,
            toggleEmailCc,
            toggleEmailBcc,
            isDropdownVisible,
            isContentDropdownVisible,
            filteredOptionFields,
            filteredContentOptionFields,
            handleBlurDropdownHide,
            addOptionFieldEdit,
            handleBlurContentDropdownHide,
            addContentOptionFieldEdit,
            debouncedGetOptionScopes,
            formatApiDataToFormData,
            formatFilesForFilePond,
        }
    },
});
</script>
<style type="text/css" scoped>
svg, .cursor-pointer {
    cursor: pointer;
}
.table__td--action {
    min-width: 70px !important;
}
.list-group-item {
    cursor: pointer;
}
.list-group-item:hover {
    background-color: #f0f0f0;
    border: none;
}
.dropdown {
    border: none; 
}
ul>li>p {
    margin-bottom: 0px;
}
/* Form disabled styles */
.form-container {
    position: relative;
}

.form-disabled {
    opacity: 0.6;
    pointer-events: none;
}

.form-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.3);
    z-index: 10;
    cursor: not-allowed;
    backdrop-filter: blur(0.5px);
}

.pointer-events-none {
    pointer-events: none;
}

/* Disabled state for form elements */
.form-disabled input,
.form-disabled select,
.form-disabled .form-control,
.form-disabled .form-select,
.form-disabled .multiselect,
.form-disabled .dropdown,
.form-disabled svg {
    pointer-events: none;
    cursor: not-allowed !important;
}

.form-disabled .multiselect {
    opacity: 0.85;
}

/* Not allowed cursor for all elements when disabled */
.form-disabled *,
.form-overlay * {
    cursor: not-allowed !important;
}

/* Hover effects for disabled state */
.form-disabled:hover,
.form-overlay:hover {
    cursor: not-allowed !important;
}

/* Additional not-allowed styling */
.form-disabled .cursor-pointer {
    cursor: not-allowed !important;
}

.form-disabled .dropdown-toggle,
.form-disabled .btn,
.form-disabled button {
    cursor: not-allowed !important;
}

/* Ensure all interactive elements show not-allowed cursor */
.form-disabled .b-dropdown,
.form-disabled .dropdown-menu-tab {
    cursor: not-allowed !important;
}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>