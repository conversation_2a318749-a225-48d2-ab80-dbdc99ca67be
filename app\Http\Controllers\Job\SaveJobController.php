<?php

namespace App\Http\Controllers\Job;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\Field\FieldRepositoryInterface;
use App\Repositories\SaveJob\SaveJobRepositoryInterface;
use App\Repositories\User\UserRepositoryInterface;
use App\Repositories\JobApprovalHistory\JobApprovalHistoryRepositoryInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Services\WorkflowProcessService;
use App\Services\FormDataProcessingService;
use App\Services\JobCreationService;
use App\Services\JobPermissionService;
use App\Services\EmailService;
use Carbon\Carbon;
use App\Models\Stage;
use App\Enums\ProcessStageStatus;
use App\Enums\SaveJobStatus;

class SaveJobController extends Controller
{
    private $fieldRepository;
    private $saveJobRepository;
    private $userRepository;
    private $jobApprovalHistoryRepository;
    private $workflowProcessService;
    private $formDataProcessingService;
    private $jobCreationService;
    private $jobPermissionService;
    private $emailService;

    public function __construct(
        FieldRepositoryInterface $fieldRepository,
        SaveJobRepositoryInterface $saveJobRepository,
        UserRepositoryInterface $userRepository,
        JobApprovalHistoryRepositoryInterface $jobApprovalHistoryRepository,
        WorkflowProcessService $workflowProcessService,
        FormDataProcessingService $formDataProcessingService,
        JobCreationService $jobCreationService,
        JobPermissionService $jobPermissionService,
        EmailService $emailService
    )
    {
        $this->fieldRepository = $fieldRepository;
        $this->saveJobRepository = $saveJobRepository;
        $this->userRepository = $userRepository;
        $this->jobApprovalHistoryRepository = $jobApprovalHistoryRepository;
        $this->workflowProcessService = $workflowProcessService;
        $this->formDataProcessingService = $formDataProcessingService;
        $this->jobCreationService = $jobCreationService;
        $this->jobPermissionService = $jobPermissionService;
        $this->emailService = $emailService;
    }

    public function storeJob(Request $request)
    {
        try {
            // Lấy tất cả dữ liệu từ request
            $request_data = $request->all();
            // Truy cập các phần tử cụ thể
            $form_id = $request_data['formId'] ?? null;
            $stage_id = $request_data['stageId'] ?? null;
            $action_id = $request_data['actionId'] ?? null;
            $key_tables = $request_data['key_tables'] ?? null; // Dữ liệu các bảng trên form
            $key_file_upload_childrens = $request_data['key_file_upload_childrens'] ?? null; // Dự liệu các file trên bảng
            $key_file_uploads = $request_data['key_file_uploads'] ?? null; // Dữ liệu các file trên form
            $form_data = $request_data['formData'] ?? null; // Dữ liệu các trường trên form
            dd($form_data, $request_data);
            $file_uploads = $request_data['fileUploads'] ?? null; // Dữ liệu file của trường files
            $form_data_job = $request_data['formDataJob'] ?? null; // Dữ liệu của bảng save_jobs

            DB::beginTransaction();

            // Lấy thông tin các trường từ form
            $fields = $this->fieldRepository->fieldsByFormIdAll($form_id);
            
            // Kiểm tra nếu có fields
            if (!$fields) {
                return response()->json([
                    'status' => 'error',
                    'error_code' => 'INVALID_FIELD_DATA',
                ], 400);
            }

            // Tạo job mới
            $current_user = Auth::user();
            $save_job = $this->jobCreationService->createJob($form_data_job, $current_user, $file_uploads);
            
            if (!$save_job) {
                return response()->json([
                    'status' => 'error',
                    'error_code' => 'NOT_FOUND_PROCESS_VERSION',
                ], 400);
            }
            
            $job_id = $save_job->id;
            
            // Xử lý dữ liệu form
            $this->formDataProcessingService->processFormData($form_data, $fields, $job_id, $key_file_uploads);
            
            // Xử lý dữ liệu bảng
            $this->formDataProcessingService->processTableData($key_tables, $fields, $job_id, $key_file_upload_childrens);
            
            // Xử lý luồng phê duyệt
            $processVersion = $save_job->processVersion;
            $processInstance = $processVersion->process;
            
            $result = $this->workflowProcessService->actionProcess(
                $job_id, 
                $fields, 
                $processInstance, 
                $stage_id, 
                $action_id, 
                $processVersion
            );

            // Kiểm tra kết quả xử lý
            if ($result === false) {
                DB::rollBack();
                Log::error("ActionProcess returned false", [
                    'job_id' => $job_id,
                    'stage_id' => $stage_id,
                    'action_id' => $action_id
                ]);
                return response()->json([
                    'status' => false,
                    'message' => 'Không thể xử lý hành động. Vui lòng kiểm tra lại thông tin stage và action.',
                    'error_code' => 'ACTION_PROCESS_FAILED'
                ], 422);
            }
            // dd($result);
            // Cam kết giao dịch
            DB::commit();
            
            return response()->json([
                'status' => 'success',
                'job_id' => $job_id,
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'error_code' => 'SERVER_ERROR',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Lấy danh sách tất cả công việc mà người dùng có thể truy cập
     * (quản lý, theo dõi, phê duyệt) trong một API duy nhất
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $dataSearch = $request->all();
        
        $result = $this->jobPermissionService->getJobsWithPermissions($user->id, $dataSearch);
        
        return response()->json([
            'status' => 'success',
            'counts' => $result['counts'],
            'jobs' => $result['jobs'],
        ], 200);
    }

    public function getJobFieldValuesByStage(Request $request, $jobId)
    {
        try {
           
            $stageId = $request->stage_id;
            
            $jobFieldValueByStage = $this->jobPermissionService->getJobFieldValuesByStage($jobId, $stageId);
            
            return response()->json([
                'status' => 'success',
                'job_field_values_by_stage' => $jobFieldValueByStage,
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => 'error',
                'error_code' => 'SERVER_ERROR',
                'error' => $th->getMessage(),
            ], 500);
        }
    }
    

    /**
     * Lấy thông tin chi tiết của một công việc cùng với thông tin quyền hạn
     */
    public function show(Request $request, $jobId)
    {
        $user = Auth::user();

        try {
            $job = $this->jobPermissionService->getJobDetail($jobId, $user->id);
            
            if (!$job) {
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy công việc.',
                    'error_code' => 'NOT_FOUND_JOB'
                ], 404);
            }
            
            // Kiểm tra quyền xem công việc
            if (!$job->permissions['can_view']) {
                return response()->json([
                    'status' => false,
                    'message' => 'Bạn không có quyền xem công việc này.',
                    'error_code' => 'FORBIDDEN'
                ], 403);
            }
            
            // Xử lý managers - lấy thông tin chi tiết người dùng từ ID
            if (isset($job->managers) && is_array($job->managers) && !empty($job->managers)) {
                $managerIds = $job->managers;
                $managers = User::whereIn('id', $managerIds)->get();
                $job->manager_users = $managers;
            }
            
            // Xử lý followers - lấy thông tin chi tiết người dùng từ ID
            if (isset($job->followers) && is_array($job->followers) && !empty($job->followers)) {
                $followerIds = $job->followers;
                $followers = User::whereIn('id', $followerIds)->get();
                $job->follower_users = $followers;
            }
            
            return response()->json([
                'status' => true,
                'message' => 'Lấy thông tin công việc thành công.',
                'data' => $job
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'error_code' => 'INTERNAL_SERVER_ERROR'
            ], 500);
        }
    }

    /**
     * Thực hiện một hành động trên công việc
     */
    public function executeAction(Request $request)
    {
        // Lấy tất cả dữ liệu từ request
        $request_data = $request->all();
        $jobId = $request_data['job_id'] ?? null;
        
        $user = Auth::user();

        try {
            DB::beginTransaction();
            
            // Lấy thông tin công việc
            $job = $this->saveJobRepository->find($jobId);
            
            if (!$job) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy công việc.',
                    'error_code' => 'NOT_FOUND_JOB'
                ], 404);
            }
            
            // Validate input data
            $stageId = $request_data['stage_id'] ?? null;
            $actionId = $request_data['action_id'] ?? null;
            $comment = $request_data['comment'] ?? null;
            $pendingApprovalId = $request_data['pending_approval_id'] ?? null;
            $conditionIds = $request_data['condition_ids'] ?? null;
            $emailConditionIds = $request_data['email_condition_ids'] ?? null;
            $key_tables = $request_data['key_tables'] ?? null; // Dữ liệu các bảng trên form
            $key_file_upload_childrens = $request_data['key_file_upload_childrens'] ?? null; // Dự liệu các file trên bảng
            $key_file_uploads = $request_data['key_file_uploads'] ?? null; // Dữ liệu các file trên form
            $form_data = $request_data['formData'] ?? null; // Dữ liệu các trường trên form
            
            if (!$stageId || !$actionId) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Thiếu thông tin stage_id hoặc action_id.',
                    'error_code' => 'INVALID_INPUT_DATA'
                ], 400);
            }
            
            // Lấy thông tin quy trình xử lý
            $processVersion = $job->processVersionWithoutActiveScope;
            if (!$processVersion) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy phiên bản quy trình.',
                    'error_code' => 'NOT_FOUND_PROCESS_VERSION'
                ], 400);
            }
            
            $processInstance = $processVersion->process;
            if (!$processInstance) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy quy trình xử lý.',
                    'error_code' => 'NOT_FOUND_PROCESS_INSTANCE'
                ], 400);
            }
            
            // Lấy thông tin các trường dữ liệu từ form
            $formId = $processVersion->form_id;
            if (!$formId) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy form_id trong quy trình.',
                    'error_code' => 'NOT_FOUND_FORM_ID'
                ], 400);
            }
            
            $fields = $this->fieldRepository->fieldsByFormIdAll($formId);

            if (!$fields) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy các trường dữ liệu của form.',
                    'error_code' => 'INVALID_FIELD_DATA',
                ], 400);
            }
            
            // Log thông tin trước khi xử lý
            Log::info("Starting executeAction", [
                'job_id' => $jobId,
                'stage_id' => $stageId,
                'action_id' => $actionId,
                'user_id' => $user->id,
                'pending_approval_id' => $pendingApprovalId,
                'condition_ids' => $conditionIds,
                'email_condition_ids' => $emailConditionIds,
            ]);

            // Xử lý dữ liệu form
            $this->formDataProcessingService->processFormData($form_data, $fields, $jobId, $key_file_uploads);
            
            // Xử lý dữ liệu bảng
            $this->formDataProcessingService->processTableData($key_tables, $fields, $jobId, $key_file_upload_childrens);
            
            // Xử lý luồng phê duyệt
            $result = $this->workflowProcessService->actionProcess(
                $jobId,
                $fields,
                $processInstance,
                $stageId,
                $actionId,
                $processVersion,
                $comment,
                $pendingApprovalId,
                $conditionIds,
                $emailConditionIds
            );

            // Kiểm tra kết quả xử lý
            if ($result === false) {
                DB::rollBack();
                Log::error("ActionProcess returned false", [
                    'job_id' => $jobId,
                    'stage_id' => $stageId,
                    'action_id' => $actionId
                ]);
                return response()->json([
                    'status' => false,
                    'message' => 'Không thể xử lý hành động. Vui lòng kiểm tra lại thông tin stage và action.',
                    'error_code' => 'ACTION_PROCESS_FAILED'
                ], 422);
            }
            
            // Commit transaction chỉ khi tất cả đều thành công
            DB::commit();
            
            Log::info("ExecuteAction completed successfully", [
                'job_id' => $jobId,
                'stage_id' => $stageId,
                'action_id' => $actionId
            ]);
            
            // Lấy thông tin job đã cập nhật
            try {
                $updatedJob = $this->jobPermissionService->getJobDetail($jobId, $user->id);
            } catch (\Exception $e) {
                Log::warning("Failed to get updated job detail", [
                    'job_id' => $jobId,
                    'error' => $e->getMessage()
                ]);
                // Vẫn trả về success vì action đã được xử lý thành công
                $updatedJob = null;
            }
            
            return response()->json([
                'status' => 'success',
                'message' => 'Thực hiện hành động thành công.',
                'data' => $updatedJob
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("ExecuteAction failed with exception", [
                'job_id' => $jobId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'error_code' => 'INTERNAL_SERVER_ERROR'
            ], 500);
        }
    }

    public function executeActionBackTo(Request $request, $jobId)
    {
        $user = Auth::user();

        try {
            DB::beginTransaction();
            
            // Lấy thông tin công việc
            $job = $this->saveJobRepository->find($jobId);
            
            if (!$job) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy công việc.',
                    'error_code' => 'NOT_FOUND_JOB'

                ], 404);
            }
            
            // Validate input data
            $stageId = $request->stage_id ?? null;
            $backToStageId = $request->back_to_stage_id ?? null;
            $actionBackToId = $request->action_back_to_id ?? null;
            $comment = $request->comment ?? null;
            $pendingApprovalId = $request->pending_approval_id ?? null;
            $emailConditionIdsBackTo = $request->email_condition_ids_back_to ?? null;
            
            if (!$stageId || !$backToStageId || !$actionBackToId) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Thiếu thông tin stage_id hoặc action_id.',
                    'error_code' => 'INVALID_INPUT_DATA'
                ], 400);
            }
            
            // Lấy thông tin quy trình xử lý
            $processVersion = $job->processVersionWithoutActiveScope;
            if (!$processVersion) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy phiên bản quy trình.'
                ], 400);
            }
            
            $processInstance = $processVersion->process;
            if (!$processInstance) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy quy trình xử lý.',
                    'error_code' => 'NOT_FOUND_PROCESS_INSTANCE'
                ], 400);
            }
            
            // Lấy thông tin các trường dữ liệu từ form
            $formId = $processVersion->form_id;
            if (!$formId) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy form_id trong quy trình.',
                    'error_code' => 'NOT_FOUND_FORM_ID'
                ], 400);
            }
            
            $fields = $this->fieldRepository->fieldsByFormIdAll($formId);
            if (!$fields) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy các trường dữ liệu của form.',
                    'error_code' => 'INVALID_FIELD_DATA',
                ], 400);
            }
            
            // Log thông tin trước khi xử lý
            Log::info("Starting executeActionBackTo", [
                'job_id' => $jobId,
                'back_to_stage_id' => $backToStageId,
                'action_back_to_id' => $actionBackToId,
                'user_id' => $user->id,
                'pending_approval_id' => $pendingApprovalId,
                'email_condition_ids_back_to' => $emailConditionIdsBackTo,
            ]);
            
            // Xử lý luồng hoàn lại bước phê duyệt
            $result = $this->processBackToAction(
                $job,
                $stageId,
                $backToStageId,
                $actionBackToId,
                $comment,
                $pendingApprovalId,
                $emailConditionIdsBackTo,
                $fields,
                $processInstance,
                $processVersion,
                $user
            );

            // Kiểm tra kết quả xử lý
            if ($result === false) {
                DB::rollBack();
                Log::error("ProcessBackToAction returned false", [
                    'job_id' => $jobId,
                    'back_to_stage_id' => $backToStageId,
                    'action_back_to_id' => $actionBackToId
                ]);
                return response()->json([
                    'status' => false,
                    'message' => 'Không thể xử lý hành động hoàn lại. Vui lòng kiểm tra lại thông tin.',
                    'error_code' => 'BACK_TO_ACTION_FAILED'
                ], 422);
            }
            
            // Commit transaction chỉ khi tất cả đều thành công
            DB::commit();
            
            Log::info("ExecuteActionBackTo completed successfully", [
                'job_id' => $jobId,
                'back_to_stage_id' => $backToStageId,
                'action_back_to_id' => $actionBackToId
            ]);
            
            // Lấy thông tin job đã cập nhật
            try {
                $updatedJob = $this->jobPermissionService->getJobDetail($jobId, $user->id);
            } catch (\Exception $e) {
                Log::warning("Failed to get updated job detail", [
                    'job_id' => $jobId,
                    'error' => $e->getMessage()
                ]);
                // Vẫn trả về success vì action đã được xử lý thành công
                $updatedJob = null;
            }
            
            return response()->json([
                'status' => 'success',
                'message' => 'Thực hiện hành động thành công.',
                'data' => $updatedJob
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("ExecuteAction failed with exception", [
                'job_id' => $jobId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'error_code' => 'INTERNAL_SERVER_ERROR'
            ], 500);
        }
    }

    /**
     * Tạo bộ lọc cho danh sách công việc
     * Cho phép người dùng lọc theo các tiêu chí khác nhau
     */
    public function getFilters(Request $request)
    {
        $user = Auth::user();
        
        try {
            // Lấy tất cả công việc của người dùng (không phân trang)
            $jobs = $this->jobPermissionService->getJobsWithPermissions($user->id, []);
            
            // Tạo bộ lọc dựa trên quyền và trạng thái
            $filters = [
                'display_status' => [
                    'pending_approval' => $jobs->where('display_status', 'pending_approval')->count(),
                    'manageable' => $jobs->where('display_status', 'manageable')->count(),
                    'view_only' => $jobs->where('display_status', 'view_only')->count(),
                    'in_progress' => $jobs->where('display_status', 'in_progress')->count(),
                    'completed' => $jobs->where('display_status', 'completed')->count(),
                    'canceled' => $jobs->where('display_status', 'canceled')->count(),
                ],
                'role' => [
                    'manager' => $jobs->where('permissions.role', 'manager')->count(),
                    'creator' => $jobs->where('permissions.role', 'creator')->count(),
                    'assignee' => $jobs->where('permissions.role', 'assignee')->count(),
                    'follower' => $jobs->where('permissions.role', 'follower')->count(),
                    'approver' => $jobs->where('permissions.role', 'approver')->count(),
                ]
            ];
            
            return response()->json([
                'status' => true,
                'message' => 'Lấy bộ lọc thành công.',
                'data' => $filters
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'error_code' => 'INTERNAL_SERVER_ERROR'
            ], 500);
        }
    }
    
    /**
     * Cập nhật thông tin công việc
     */
    public function update(Request $request, $jobId)
    {
        $user = Auth::user();
        
        try {
            $job = $this->jobPermissionService->getJobDetail($jobId, $user->id);
            
            if (!$job) {
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy công việc.',
                    'error_code' => 'NOT_FOUND_JOB'
                ], 404);
            }

            // Kiểm tra quyền chỉnh sửa công việc
            if (!$job->permissions['can_manager']) {
                return response()->json([
                    'status' => false,
                    'message' => 'Bạn không có quyền chỉnh sửa công việc này.',
                    'error_code' => 'FORBIDDEN'
                ], 403);
            }
            
            // Xác thực request
            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:200',
                'description' => 'sometimes|nullable|string',
                'status' => 'sometimes|required|string|in:pending,processing,completed,cancel',
                'user_id' => 'sometimes|required|string|uuid',
                'department_id' => 'sometimes|required|string|uuid',
                'job_position_id' => 'sometimes|required|string|uuid',
                'files' => 'sometimes|nullable|array',
                'managers' => 'sometimes|nullable|array',
                'followers' => 'sometimes|nullable|array',
            ]);
            
            // Thực hiện cập nhật công việc
            $updatedJob = $this->saveJobRepository->update($validated, $jobId);
            
            return response()->json([
                'status' => true,
                'message' => 'Cập nhật công việc thành công.',
                'data' => $this->jobPermissionService->getJobDetail($jobId, $user->id)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'error_code' => 'INTERNAL_SERVER_ERROR'
            ], 500);
        }
    }
    
    /**
     * Xóa công việc
     */
    public function destroy(Request $request, $jobId)
    {
        $user = Auth::user();
        
        try {
            $job = $this->jobPermissionService->getJobDetail($jobId, $user->id);
            
            if (!$job) {
                return response()->json([
                    'status' => false,
                    'message' => 'Không tìm thấy công việc.',
                    'error_code' => 'NOT_FOUND_JOB'
                ], 404);
            }
            
            // Kiểm tra quyền xóa công việc
            if (!$job->permissions['can_manager']) {
                return response()->json([
                    'status' => false,
                    'message' => 'Bạn không có quyền xóa công việc này.',
                    'error_code' => 'FORBIDDEN'
                ], 403);
            }
            
            // Thực hiện xóa công việc
            $this->saveJobRepository->delete($jobId);
            
            return response()->json([
                'status' => true,
                'message' => 'Xóa công việc thành công.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'error_code' => 'INTERNAL_SERVER_ERROR'
            ], 500);
        }
    }

    /**
     * Xử lý luồng hoàn lại bước phê duyệt
     */
    private function processBackToAction($job, $stageId, $backToStageId, $actionBackToId, $comment, $pendingApprovalId, $emailConditionIdsBackTo, $fields, $processInstance, $processVersion, $user)
    {
        // 1. Cập nhật thông tin về bản ghi pendingApprovalId với $actionBackToId
        // 2. Tạo thêm bản ghi mới trong bảng JobApprovalHistory cho backToStageId với các trường hợp khác config('constants.stage.START')
        // 3. Cập nhật trạng thái của bảng save_jobs là processing, nếu $backToStageId bằng với config('constants.stage.START') thì cập nhật trạng thái của save_jobs là pending
        // 4. Cập nhật trạng thái trong ProcessInstanceStageStatus là processing, nếu $backToStageId bằng với config('constants.stage.START') thì cập nhật tất cả là pending
        try {
            $jobId = $job->id;

            // 1. Cập nhật thông tin về bản ghi pendingApprovalId
            if ($pendingApprovalId) {
                $jobApprovalHistory = $this->jobApprovalHistoryRepository->find($pendingApprovalId);
                if ($jobApprovalHistory) {
                    $updateData = [
                        'user_id' => $user->id,
                        'action_id' => $actionBackToId,
                        'date' => Carbon::now()->toDateTimeString(),
                        'comment' => $comment,
                    ];

                    $this->jobApprovalHistoryRepository->update($updateData, $pendingApprovalId);

                    Log::info("Updated pending approval history", [
                        'job_id' => $jobId,
                        'pending_approval_id' => $pendingApprovalId,
                        'action_back_to_id' => $actionBackToId
                    ]);
                } else {
                    Log::warning("Pending approval history not found", [
                        'pending_approval_id' => $pendingApprovalId
                    ]);
                    return false;
                }
            }

            // 2. Tạo thêm bản ghi mới trong bảng JobApprovalHistory cho backToStageId
            if ($backToStageId !== config('constants.stage.START')) {
                $newHistoryData = [
                    'job_id' => $jobId,
                    'stage_id' => $backToStageId,
                ];
    
                // Lấy thông tin stage để xác định approvers và followers
                $stage = Stage::find($backToStageId);
                if ($stage) {
                    $approvedList = $this->userRepository->getUserByOptionScopeRes($stage->approver ?? []);
                    $followers = $this->userRepository->getUserByOptionScopeRes($stage->followers ?? []);
    
                    if (!empty($approvedList)) {
                        $newHistoryData['approved_list'] =  array_column($approvedList, 'value');
                    }
                    if (!empty($followers)) {
                        $newHistoryData['followers'] = array_column($followers, 'value');
                    }
                }
    
                $newHistory = $this->jobApprovalHistoryRepository->create($newHistoryData);
    
                if (!$newHistory) {
                    Log::error("Failed to create new approval history for back to stage", [
                        'job_id' => $jobId,
                        'back_to_stage_id' => $backToStageId
                    ]);
                    return false;
                }
    
                Log::info("Created new approval history for back to stage", [
                    'job_id' => $jobId,
                    'back_to_stage_id' => $backToStageId,
                    'new_history_id' => $newHistory->id
                ]);
            }

            // 3. Cập nhật trạng thái trong ProcessInstanceStageStatus
            $this->updateStageStatus($job, $stageId, $backToStageId);

            // 4. Thực hiện chức năng gửi mail và kiểm tra điều kiện
            $this->processBackToEmail($job, $stageId, $backToStageId, $actionBackToId, $emailConditionIdsBackTo, $fields, $processInstance, $processVersion, $job->user);

            return true;

        } catch (\Exception $e) {
            Log::error("ProcessBackToAction failed", [
                'job_id' => $job->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Cập nhật trạng thái giai đoạn trong ProcessInstanceStageStatus
     */
    private function updateStageStatus($job, $stageId, $backToStageId)
    {
        try {
            $stageStatusStage = $job->processInstanceStageStatus()->where('stage_id', $stageId)->first();
            $stageStatusBackToStage = $job->processInstanceStageStatus()->where('stage_id', $backToStageId)->first();
            if ($stageStatusStage && $stageStatusBackToStage) {
                if ($backToStageId === config('constants.stage.START')) {
                    $job->processInstanceStageStatus()->update(['status' => ProcessStageStatus::PENDING->value]);
                } else {
                    $stageStatusStage->status = ProcessStageStatus::PENDING->value;
                    $stageStatusStage->save();
                    $stageStatusBackToStage->status = ProcessStageStatus::PROCESSING->value;
                    $stageStatusBackToStage->save();
                }
            }

            // Cập nhật trạng thái job
            if ($backToStageId === config('constants.stage.START')) {
                $job->status = SaveJobStatus::PENDING->value;
            } else {
                $job->status = SaveJobStatus::PROCESSING->value;
            }
            $job->save();
        } catch (\Exception $e) {
            Log::error("Failed to update stage status", [
                'job_id' => $job->id,
                'back_to_stage_id' => $backToStageId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Xử lý gửi email cho hành động hoàn lại - sử dụng WorkflowProcessService
     */
    private function processBackToEmail($job, $stageId, $backToStageId, $actionBackToId, $emailConditionIdsBackTo, $fields, $processInstance, $processVersion, $user)
    {
        try {
            // Lấy field values từ job (giống WorkflowProcessService)
            $fieldValues = $job->jobFieldValues
                ->pluck('field_value', 'field_id')
                ->mapWithKeys(function ($value, $key) use ($fields) {
                    return [$fields->where('id', $key)->value('keyword') => $value];
                })
                ->toArray();

            // Kiểm tra điều kiện email cho action back to
            $emailStageIds = $this->emailService->getEmailInStage(
                $fieldValues,
                $processInstance,
                $stageId,
                $actionBackToId,
                $processVersion,
                $user,
                $emailConditionIdsBackTo
            );
            
            // Gửi email nếu có template thỏa mãn điều kiện
            if (!empty($emailStageIds)) {
                $emailResult = $this->workflowProcessService->processEmailSending(
                    $fieldValues,
                    $fields,
                    $emailStageIds,
                    $job->id,
                    [$backToStageId]
                );

                if ($emailResult) {
                    Log::info("Email sent successfully for back to action", [
                        'job_id' => $job->id,
                        'back_to_stage_id' => $backToStageId,
                        'email_template_ids' => $emailStageIds
                    ]);
                } else {
                    Log::warning("Failed to send email for back to action", [
                        'job_id' => $job->id,
                        'back_to_stage_id' => $backToStageId,
                        'email_template_ids' => $emailStageIds
                    ]);
                }
            } else {
                Log::info("No email templates found for back to action", [
                    'job_id' => $job->id,
                    'back_to_stage_id' => $backToStageId,
                    'action_back_to_id' => $actionBackToId
                ]);
            }

        } catch (\Exception $e) {
            Log::error("ProcessBackToEmail failed", [
                'job_id' => $job->id,
                'error' => $e->getMessage()
            ]);
            // Email thất bại không làm fail toàn bộ process
        }
    }


}
