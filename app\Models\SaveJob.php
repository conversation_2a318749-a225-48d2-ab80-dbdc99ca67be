<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use App\Models\Scopes\ActiveScope;

class SaveJob extends Model
{
    use SoftDeletes, HasUuids;
    protected $table = 'save_jobs';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'user_id',
        'department_id',
        'job_position_id',
        'process_version_id',
        'status',
        'description',
        'files',
        'followers',
        'managers',
        'create_by',
    ];

    protected $casts = [
        'files' => 'array',          // Chuyển đổi cột 'files' (kiểu JSON) thành mảng PHP
        'followers' => 'array',      // Chuyển đổi cột 'followers' (kiểu JSON) thành mảng PHP
        'managers' => 'array',       // Chuyển đổi cột 'managers' (kiể<PERSON> JSON) thành mảng PHP
        'created_at' => 'datetime',  // Đảm bảo 'created_at' được coi là đối tượng Carbon/DateTime
        'updated_at' => 'datetime',  // Đảm bảo 'updated_at' được coi là đối tượng Carbon/DateTime
        'deleted_at' => 'datetime',  // Cần thiết khi sử dụng SoftDeletes
    ];

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id', 'id');
    }

    public function jobFieldValues()
    {
        return $this->hasMany('App\Models\JobFieldValue', 'job_id', 'id');
    }

    public function jobApprovalHistories()
    {
        return $this->hasMany('App\Models\JobApprovalHistory', 'job_id', 'id');
    }

    public function jobPosition()
    {
        // Giả sử model JobPosition nằm trong App\Models\JobPosition
        // và khóa ngoại trong bảng 'save_jobs' là 'job_position_id'
        // và khóa chính trong bảng 'job_positions' là 'id' (mặc định)
        return $this->belongsTo(JobPosition::class, 'job_position_id');
    }

    public function processVersion()
    {
        return $this->belongsTo(ProcessVersion::class, 'process_version_id');
    }

    public function processVersionWithoutActiveScope()
    {
        return $this->processVersion()->withoutGlobalScope(ActiveScope::class);
    }

    public function department() 
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function assignedUser() 
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function createdBy() // Người tạo record (create_by)
    {
        return $this->belongsTo(User::class, 'create_by');
    }

    public function approvalHistories()
    {
        return $this->hasMany(JobApprovalHistory::class, 'job_id', 'id');
    }

    public function processInstanceStageStatus()
    {
        return $this->hasMany(ProcessInstanceStageStatus::class, 'job_id', 'id');
    }

    // Helper methods để kiểm tra quyền truy cập
    public function isManager(?User $user = null): bool
    {
        $user = $user ?: Auth::user();
        if (!$user) return false;
        return in_array($user->id, $this->managers ?? []);
    }

    public function isFollower(?User $user = null): bool
    {
        $user = $user ?: Auth::user();
        if (!$user) return false;
        return in_array($user->id, $this->followers ?? []);
    }

    public function isCreator(?User $user = null): bool
    {
        $user = $user ?: Auth::user();
        if (!$user) return false;
        return $user->id === $this->create_by;
    }

    public function isAssignee(?User $user = null): bool
    {
        $user = $user ?: Auth::user();
        if (!$user) return false;
        return $user->id === $this->user_id;
    }

    public function canBeViewedBy(?User $user = null): bool
    {
        $user = $user ?: Auth::user();
        if (!$user) return false;

        return $this->isManager($user) || 
               $this->isFollower($user) || 
               $this->isCreator($user) || 
               $this->isAssignee($user) ||
               $this->userInApprovalProcess($user);
    }

    public function userInApprovalProcess(?User $user = null): bool
    {
        $user = $user ?: Auth::user();
        if (!$user) return false;
        
        foreach ($this->jobApprovalHistories as $history) {
            if (in_array($user->id, $history->approved_list ?? []) || 
                in_array($user->id, $history->followers ?? [])) {
                return true;
            }
        }
        
        return false;
    }
    /**
     * Lấy tất cả các stage mà user có thể phê duyệt
     */
    public function getAllApprovalStagesForUser($user)
    {
        $user = $user ?: Auth::user();
        if (!$user) return collect();

        return $this->jobApprovalHistories()
            ->whereNull('user_id')  // Chưa được phê duyệt
            ->whereJsonContains('approved_list', $user->id)
            ->orderBy('created_at', 'asc')
            ->get();
    }
}
