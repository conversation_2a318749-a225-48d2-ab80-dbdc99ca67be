<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\ProcessConditionService;

class ProcessConditionServiceContextTest extends TestCase
{
    // Không sử dụng RefreshDatabase để tránh ảnh hưởng DB

    private ProcessConditionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ProcessConditionService();
    }

    /**
     * Test với process context - user fields
     */
    public function test_process_context_user_fields()
    {
        // Tạo mock user data
        $userData = (object) [
            'id' => 1,
            'name' => 'Test User',
            'department_id' => 10,
            'position_id' => 5,
            'rank_id' => 3
        ];

        // Tạo mock process data
        $processData = (object) [
            'id' => 1,
            'name' => 'Test Process',
            'description' => 'Test Description',
            'created_by' => 1
        ];

        $contextData = [
            'user' => $userData,
            'process' => $processData
        ];

        // Test department_ids field
        $this->assertTrue($this->evaluateProcessCondition('department_ids', 'equal', 10, $contextData));
        $this->assertFalse($this->evaluateProcessCondition('department_ids', 'equal', 20, $contextData));
        $this->assertTrue($this->evaluateProcessCondition('department_ids', 'in', [10, 20, 30], $contextData));

        // Test job_position_ids field
        $this->assertTrue($this->evaluateProcessCondition('job_position_ids', 'equal', 5, $contextData));
        $this->assertTrue($this->evaluateProcessCondition('job_position_ids', 'in', [5, 6, 7], $contextData));

        // Test rank_ids field
        $this->assertTrue($this->evaluateProcessCondition('rank_ids', 'equal', 3, $contextData));
        $this->assertTrue($this->evaluateProcessCondition('rank_ids', 'not_in', [1, 2, 4], $contextData));
    }

    /**
     * Test với process context - process fields
     */
    public function test_process_context_process_fields()
    {
        $userData = (object) ['id' => 1];
        $processData = (object) [
            'id' => 1,
            'name' => 'Workflow Test Process',
            'description' => 'This is a test process for workflow',
            'created_by' => 123
        ];

        $contextData = [
            'user' => $userData,
            'process' => $processData
        ];

        // Test name field
        $this->assertTrue($this->evaluateProcessCondition('name', 'equal', 'Workflow Test Process', $contextData));
        $this->assertTrue($this->evaluateProcessCondition('name', 'contain', 'Test', $contextData));
        $this->assertFalse($this->evaluateProcessCondition('name', 'contain', 'xyz', $contextData));

        // Test description field
        $this->assertTrue($this->evaluateProcessCondition('description', 'contain', 'workflow', $contextData));
        $this->assertTrue($this->evaluateProcessCondition('description', 'not_empty', null, $contextData));

        // Test created_by field
        $this->assertTrue($this->evaluateProcessCondition('created_by', 'equal', 123, $contextData));
        $this->assertTrue($this->evaluateProcessCondition('created_by', 'in', [123, 456, 789], $contextData));
    }

    /**
     * Test với context data không đầy đủ
     */
    public function test_incomplete_context_data()
    {
        // Context thiếu user
        $contextData = [
            'process' => (object) ['name' => 'Test']
        ];

        $this->assertNull($this->getContextValue('department_ids', $contextData));
        $this->assertNull($this->getContextValue('name', $contextData));

        // Context thiếu process
        $contextData = [
            'user' => (object) ['department_id' => 10]
        ];

        $this->assertNull($this->getContextValue('department_ids', $contextData));
        $this->assertNull($this->getContextValue('name', $contextData));

        // Context rỗng
        $this->assertNull($this->getContextValue('department_ids', []));
    }

    /**
     * Test với field không được hỗ trợ
     */
    public function test_unsupported_fields()
    {
        $contextData = [
            'user' => (object) ['department_id' => 10],
            'process' => (object) ['name' => 'Test']
        ];

        // Field không có trong mapping
        $this->assertNull($this->getContextValue('unsupported_field', $contextData));
        $this->assertNull($this->getContextValue('random_field', $contextData));
    }

    /**
     * Test với object types khác nhau
     */
    public function test_different_object_types()
    {
        // Array context
        $arrayContext = [
            'user' => [
                'department_id' => 10,
                'position_id' => 5
            ],
            'process' => [
                'name' => 'Array Process',
                'description' => 'Test'
            ]
        ];

        $this->assertEquals(10, $this->getContextValue('department_ids', $arrayContext));
        $this->assertEquals('Array Process', $this->getContextValue('name', $arrayContext));

        // Object context (đã test ở trên)
        $objectContext = [
            'user' => (object) [
                'department_id' => 20,
                'position_id' => 6
            ],
            'process' => (object) [
                'name' => 'Object Process',
                'description' => 'Test'
            ]
        ];

        $this->assertEquals(20, $this->getContextValue('department_ids', $objectContext));
        $this->assertEquals('Object Process', $this->getContextValue('name', $objectContext));
    }

    /**
     * Test exception handling trong getContextValue
     */
    public function test_context_value_exception_handling()
    {
        // Object không có property
        $contextData = [
            'user' => (object) [],
            'process' => (object) []
        ];

        $this->assertNull($this->getContextValue('department_ids', $contextData));
        $this->assertNull($this->getContextValue('name', $contextData));

        // Invalid object structure
        $contextData = [
            'user' => 'not_an_object',
            'process' => 123
        ];

        $this->assertNull($this->getContextValue('department_ids', $contextData));
        $this->assertNull($this->getContextValue('name', $contextData));
    }

    /**
     * Test integration với evaluateSingleCondition
     */
    public function test_integration_with_evaluate_condition()
    {
        $userData = (object) [
            'department_id' => 10,
            'position_id' => 5,
            'rank_id' => 3
        ];

        $processData = (object) [
            'name' => 'Integration Test Process',
            'description' => 'Test Description',
            'created_by' => 100
        ];

        $contextData = [
            'user' => $userData,
            'process' => $processData
        ];

        // Test với các operator khác nhau
        $this->assertTrue($this->evaluateProcessCondition('department_ids', 'equal', 10, $contextData));
        $this->assertTrue($this->evaluateProcessCondition('department_ids', 'greater', 5, $contextData));
        $this->assertTrue($this->evaluateProcessCondition('department_ids', 'between', [5, 15], $contextData));
        $this->assertTrue($this->evaluateProcessCondition('name', 'contain', 'Integration', $contextData));
        $this->assertFalse($this->evaluateProcessCondition('name', 'empty', null, $contextData));
    }

    /**
     * Test field mapping accuracy
     */
    public function test_field_mapping_accuracy()
    {
        $userData = (object) [
            'department_id' => 100,
            'position_id' => 200,
            'rank_id' => 300
        ];

        $contextData = [
            'user' => $userData,
            'process' => (object) []
        ];

        // Verify correct field mapping
        $this->assertEquals(100, $this->getContextValue('department_ids', $contextData));
        $this->assertEquals(200, $this->getContextValue('job_position_ids', $contextData));
        $this->assertEquals(300, $this->getContextValue('rank_ids', $contextData));

        // Verify wrong field names return null
        $this->assertNull($this->getContextValue('department_id', $contextData)); // Should be department_ids
        $this->assertNull($this->getContextValue('position_id', $contextData)); // Should be job_position_ids
    }

    /**
     * Helper method để test process conditions
     */
    private function evaluateProcessCondition(string $field, string $operator, $parameter, array $contextData): bool
    {
        $condition = [
            'f' => $field,
            'o' => $operator,
            'p' => $parameter
        ];

        return $this->service->evaluateSingleCondition($condition, $contextData, 'process');
    }

    /**
     * Helper method để test getContextValue directly
     */
    private function getContextValue(string $field, array $contextData)
    {
        return $this->service->getContextValue($contextData, $field);
    }
}
