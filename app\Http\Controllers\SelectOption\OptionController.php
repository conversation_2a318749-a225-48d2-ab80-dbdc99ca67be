<?php

namespace App\Http\Controllers\SelectOption;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\Process\ProcessRepositoryInterface;
use App\Repositories\ProcessGroup\ProcessGroupRepositoryInterface;
use App\Repositories\User\UserRepositoryInterface;
use App\Models\User;
use App\Models\Department;
use App\Models\Rank;
use App\Models\JobPosition;

class OptionController extends Controller
{
    private $processRepository;
    private $processGroupRepository;
    private $userRepository;

    public function __construct(
        ProcessRepositoryInterface $processRepository,
        ProcessGroupRepositoryInterface $processGroupRepository,
        UserRepositoryInterface $userRepository
    ) 
    {
        $this->processRepository = $processRepository;
        $this->processGroupRepository = $processGroupRepository;
        $this->userRepository = $userRepository;
    }

    public function getOptionUsers(Request $request)
    {
        $search = $request['query'];

        // Xây dựng truy vấn
        $query = User::select('id', 'account_name', 'full_name');

        // Thêm điều kiện tìm kiếm nếu có từ khóa
        if ($search) {
            $query->where('full_name', 'LIKE', '%' . $search . '%');
        }

        // Lấy dữ liệu từ cột đã chọn
        $data = $query->when(!empty($search), function($query) { $query->limit(20); })->get();

        return response()->json($data);
    }

    public function getOptionDepartments()
    {
        // Xây dựng truy vấn
        $query = Department::select('id', 'name', 'type_department_id')
            ->with(['typeDepartment' => function ($query) {
                $query->select('id', 'name'); // Chỉ lấy các cột cần thiết từ TypeDepartment
            }]);

        // Lấy dữ liệu từ cột đã chọn
        $data = $query->get();

        // Định dạng lại dữ liệu trả về
        $formatted_data = $data->map(function ($department) {
            return [
                'id' => $department->id,
                'name' => $department->name,
                'type' => $department->typeDepartment ? $department->typeDepartment->name : null, // Lấy tên từ TypeDepartment nếu tồn tại
            ];
        });
        
        // Trả về dữ liệu dưới dạng JSON
        return response()->json($formatted_data);
    }

    public function getJobPositionSystems()
    {
        // Xây dựng truy vấn
        $query = JobPosition::select('id', 'name');

        // Lấy dữ liệu từ cột đã chọn
        $data = $query->get();
        
        // Trả về dữ liệu dưới dạng JSON
        return response()->json($data);
    }

    public function getRankSystems()
    {
        // Xây dựng truy vấn
        $query = Rank::select('id', 'name');

        // Lấy dữ liệu từ cột đã chọn
        $data = $query->get();
        
        // Trả về dữ liệu dưới dạng JSON
        return response()->json($data);
    }

    public function getOptionWorkflows(Request $request)
    {
        try {
            $search = $request['query'];
            $workflows = $this->processRepository->getWorkflowByScopeUses($search);
            
            return response()->json($workflows);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'message' => __('error.500'),
            ], 500);
        }
    }

    public function getOptionProcessGroups(Request $request)
    {
        $search = $request['query'];
        $process_groups = $this->processGroupRepository->getProcessGroupByScopeUses($search);
        // dd($process_groups);
        return response()->json($process_groups);
    }

    public function getOptionScopes(Request $request)
    {
        $prefixes = config('constants.prefixes'); // Lấy các prefix dùng chung

        $U   = $prefixes['U'];
        $D   = $prefixes['D'];
        $R   = $prefixes['R'];
        $RD  = $prefixes['RD'];
        $RB  = $prefixes['RB'];
        $RC  = $prefixes['RC'];
        $JP  = $prefixes['JP'];
        $JPD = $prefixes['JPD'];
        $JPB = $prefixes['JPB'];
        $JPC = $prefixes['JPC'];

        $search = $request['query'];
        $results = []; // Mảng để lưu trữ kết quả tìm kiếm

        // Danh sách các model và các trường cần tìm kiếm
        $models = [
            User::class => ['account_name', 'full_name'],
            Department::class => ['name'],
            Rank::class => ['name'],
            JobPosition::class => ['name'],
        ];
        // Lặp qua từng model và thực hiện tìm kiếm
        foreach ($models as $model => $fields) {
            // Kiểm tra nếu model là Department thì thêm type_department_id vào select
            if ($model === Department::class) {
                $query = $model::select('id', 'name', 'type_department_id')
                    ->with(['typeDepartment' => function ($query) {
                        $query->select('id', 'name'); // Chỉ lấy các cột cần thiết từ TypeDepartment
                    }]);
            } else if ($model === User::class) {
                $query = $model::select('id', 'account_name', 'full_name as name'); // Model User chỉ lấy theo full_name nhưng trả về name
            } else {
                $query = $model::select('id', 'name'); // Các model khác chỉ lấy id và name
            }
        
            // Thêm điều kiện tìm kiếm cho từng trường
            $query->where(function ($q) use ($fields, $search) {
                foreach ($fields as $field) {
                    $q->orWhere($field, 'LIKE', '%' . $search . '%');
                }
            });
        
            // Lấy dữ liệu và thêm thông tin mô tả vào từng item
            $model_results = $query->when(!empty($search), function($query) { $query->limit(20); })->get()->flatMap(function ($item) use ($model, $U, $D, $R, $RD, $RB, $RC, $JP, $JPD, $JPB, $JPC) {
                $description = '';
            
                // Xử lý mô tả cho từng model
                switch ($model) {
                    case User::class:
                        $description = __('messages.option_scopes.user_description');
                        $model_symbol = $U; //User
                        return [
                            [
                                'label' => $item->account_name . ' - ' .$item->name,
                                'description' => $description,
                                'value' => $model_symbol . $item->id,
                            ],
                        ];
                    case Department::class:
                        $description = __('messages.option_scopes.department_description');
                        $model_symbol = $D; //Department

                        // Lấy name từ typeDepartment nếu tồn tại
                        $type_department_name = $item->typeDepartment ? $item->typeDepartment->name : null;
                        $description .= ' - ' . $type_department_name;

                        return [
                            [
                                'label' => $item->name,
                                'description' => $description,
                                'value' => $model_symbol . $item->id,
                            ],
                        ];
                    case Rank::class:
                        $description = __('messages.option_scopes.rank_description');
                        $model_symbol = $R; //Rank
                        $model_symbol_same_department = $RD; //Rank with same department
                        $model_symbol_same_branch = $RB; //Rank with same branch
                        $model_symbol_same_company = $RC; //Rank with same company
                        return [
                            [
                                'label' => $item->name,
                                'description' => $description,
                                'value' => $model_symbol . $item->id, // Bản gốc
                            ],
                            [
                                'label' => $item->name . ' ' . __('messages.option_scopes.same_department'),
                                'description' => $description . ' ' . __('messages.option_scopes.same_department'),
                                'value' => $model_symbol_same_department . $item->id, // Cùng phòng ban
                            ],
                            [
                                'label' => $item->name . ' ' . __('messages.option_scopes.same_branch'),
                                'description' => $description . ' ' . __('messages.option_scopes.same_branch'),
                                'value' => $model_symbol_same_branch . $item->id, // Cùng chi nhánh
                            ],
                            [
                                'label' => $item->name . ' ' . __('messages.option_scopes.same_company'),
                                'description' => $description . ' ' . __('messages.option_scopes.same_company'),
                                'value' => $model_symbol_same_company . $item->id, // Cùng công ty
                            ],
                        ];
                    case JobPosition::class:
                        $description = __('messages.option_scopes.job_position_description');
                        $model_symbol = $JP; //JobPosition
                        $model_symbol_same_department = $JPD; //JobPosition with same department
                        $model_symbol_same_branch = $JPB; //JobPosition with same branch
                        $model_symbol_same_company = $JPC; //JobPosition with same company
                        return [
                            [
                                'label' => $item->name,
                                'description' => $description,
                                'value' => $model_symbol . $item->id, // Bản gốc
                            ],
                            [
                                'label' => $item->name . ' ' . __('messages.option_scopes.same_department'),
                                'description' => $description . ' ' . __('messages.option_scopes.same_department'),
                                'value' => $model_symbol_same_department . $item->id, // Cùng phòng ban
                            ],
                            [
                                'label' => $item->name . ' ' . __('messages.option_scopes.same_branch'),
                                'description' => $description . ' ' . __('messages.option_scopes.same_branch'),
                                'value' => $model_symbol_same_branch . $item->id, // Cùng chi nhánh
                            ],
                            [
                                'label' => $item->name . ' ' . __('messages.option_scopes.same_company'),
                                'description' => $description . ' ' . __('messages.option_scopes.same_company'),
                                'value' => $model_symbol_same_company . $item->id, // Cùng công ty
                            ],
                        ];
                }
            });
        
            $results = array_merge($results, $model_results->toArray());
        }
        // dd($results);
        // Trả về kết quả dưới dạng JSON
        return response()->json($results);
    }

    public function getUserByOptionScopes(Request $request)
    {
        $option_scopes = $request['optionScopes'];
        $workflow_id = $request['workflowId'];
        $result_users = $this->userRepository->getUserByOptionScopeRes($option_scopes, $workflow_id);
        // dd($result_users);
        return response()->json($result_users);
    }
}
