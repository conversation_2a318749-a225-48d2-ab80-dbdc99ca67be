<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Passport\HasApiTokens;
use App\Traits\ActiveGlobalScopeTrait;
use App\Traits\HasTenantTrait;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Models\Scopes\TenantScope;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasUuids, HasFactory, Notifiable, ActiveGlobalScopeTrait, HasTenantTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $fillable = [
        'account_name',
        'full_name',
        'email',
        'email_verified_at',
        'password',
        'two_factor_code',
        'two_factor_expires_at',
        'department_id',
        'rank_id',
        'job_position_id',
        'cost_center_id',
        'is_active',
        'not_activated',
        'activation_date',
        'language',
        'color_id',
        'tenant_id',
        'create_by',
        'roles_updated_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'roles_updated_at' => 'datetime',
    ];

    public static function getDescription()
    {
        return __('tables.users');
    }

    public static function getColumnDescriptions()
    {
        return __('columns.users');
    }

    public function department()
    {
        return $this->belongsTo('App\Models\Department', 'department_id', 'id');
    }

    public function rank()
    {
        return $this->belongsTo('App\Models\Rank', 'rank_id', 'id');
    }
    
    public function jobPosition()
    {
        return $this->belongsTo('App\Models\JobPosition', 'job_position_id', 'id');
    }
    
    public function createBy()
    {
        return $this->belongsTo(User::class, 'create_by');
    }

    public function roles()
    {
        // Bỏ global scope tenant để lấy được tất cả vai trò của user quản trị
        return $this->belongsToMany(Role::class, 'role_user')
            ->using(\App\Models\RoleUser::class)
            ->withoutGlobalScope(TenantScope::class) 
            ->withTimestamps();
    }
    
    /**
     * Cập nhật vai trò cho người dùng
     *
     * @param array $roleIds Mảng ID của các vai trò
     * @return bool
     */
    public function syncRoles(array $roleIds)
    {
        $this->roles()->sync($roleIds);
        $this->roles_updated_at = now();
        return $this->save();
    }
    
    /**
     * Thêm vai trò cho người dùng
     *
     * @param array|string|Role $roles
     * @return void
     */
    public function assignRoles($roles)
    {
        if (is_string($roles)) {
            $roles = Role::where('name', $roles)->get();
        }
        
        if ($roles instanceof Role) {
            $roles = collect([$roles]);
        }
        
        if (is_array($roles)) {
            $roles = collect($roles);
        }
        
        $this->roles()->syncWithoutDetaching($roles->pluck('id')->toArray());
        $this->roles_updated_at = now();
        $this->save();
    }
    
    /**
     * Xóa vai trò khỏi người dùng
     *
     * @param array|string|Role $roles
     * @return void
     */
    public function removeRoles($roles)
    {
        if (is_string($roles)) {
            $roles = Role::where('name', $roles)->get();
        }
        
        if ($roles instanceof Role) {
            $roles = collect([$roles]);
        }
        
        if (is_array($roles)) {
            $roles = collect($roles);
        }
        
        $this->roles()->detach($roles->pluck('id')->toArray());
        $this->roles_updated_at = now();
        $this->save();
    }
    
    /**
     * Kiểm tra người dùng có vai trò cụ thể không
     *
     * @param string|Role $role
     * @return bool
     */
    public function hasRole($role)
    {
        if (is_string($role)) {
            return $this->roles->contains('name', $role);
        }
        
        if ($role instanceof Role) {
            return $this->roles->contains('id', $role->id);
        }
        
        return false;
    }
    
    /**
     * Kiểm tra người dùng có quyền cụ thể không
     * (Kiểm tra qua vai trò của người dùng)
     *
     * @param string $permission Tên quyền cần kiểm tra
     * @return bool
     */
    public function hasPermission($permission)
    {
        foreach ($this->roles as $role) {
            if ($role->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Lấy tất cả quyền của người dùng thông qua vai trò
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllPermissions()
    {
        $permissions = collect([]);
        foreach ($this->roles as $role) {
            $permissions = $permissions->merge($role->permissions);
        }
        
        return $permissions->unique('id');
    }
    
    /**
     * Lấy timestamp phiên bản quyền mới nhất
     * (Lấy từ roles_updated_at thay vì permissions_updated_at)
     * 
     * @return int
     */
    public function getPermissionsVersion()
    {
        return optional($this->roles_updated_at)->timestamp ?? 0;
    }
}
