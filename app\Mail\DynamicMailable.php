<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Support\Facades\Log;

class DynamicMailable extends Mailable
{
    use Queueable, SerializesModels;

    public string $finalSubject;         // Tiêu đề email cuối cùng
    public string $finalHtmlContent;     // Nội dung HTML cuối cùng của email
    public array $finalAttachmentPaths; // Mảng các đường dẫn tuyệt đối tới file đính kèm
    public ?string $finalFromAddress;   // Địa chỉ email "From" cuối cùng (ví dụ: "<EMAIL>")
    public ?string $finalFromName;      // Tên người gửi "From" cuối cùng
    public ?string $jobDetailUrl;       // URL chi tiết công việc

    /**
     * Create a new message instance.
     */
    public function __construct(
        string $subject,
        string $htmlContent,
        array $attachmentPaths = [],
        ?string $fromAddress = null,
        ?string $fromName = null,
        ?string $jobDetailUrl = null
    ) {
        $this->finalSubject = $subject;
        $this->finalHtmlContent = $htmlContent;
        $this->finalAttachmentPaths = $attachmentPaths;
        $this->finalFromAddress = $fromAddress;
        $this->finalFromName = $fromName;
        $this->jobDetailUrl = $jobDetailUrl;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $envelope = new Envelope(
            subject: $this->finalSubject,
        );

        // Chỉ thiết lập "From" nếu địa chỉ "From" tùy chỉnh được cung cấp và hợp lệ
        if ($this->finalFromAddress && filter_var($this->finalFromAddress, FILTER_VALIDATE_EMAIL)) {
            // Nếu có cả tên người gửi, sử dụng nó
            $fromName = $this->finalFromName ?: null; // Sử dụng tên nếu có, ngược lại là null
            $envelope->from(new Address($this->finalFromAddress, $fromName));
        }
        // Nếu $this->finalFromAddress là null hoặc không hợp lệ,
        // Laravel sẽ tự động sử dụng MAIL_FROM_ADDRESS và MAIL_FROM_NAME từ file .env.

        return $envelope;
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.dynamic', // Sử dụng view chung resources/views/emails/dynamic.blade.php
            with: [
                'htmlContent' => $this->finalHtmlContent, // Truyền nội dung HTML đã chuẩn bị vào view
                'jobDetailUrl' => $this->jobDetailUrl,    // Truyền URL chi tiết công việc vào view
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];
        // Duyệt qua danh sách các đường dẫn file tuyệt đối đã được chuẩn bị
        foreach ($this->finalAttachmentPaths as $path) {
            // Kiểm tra lại tính hợp lệ của đường dẫn file
            if (is_string($path) && file_exists($path) && is_readable($path)) {
                try {
                    // Tạo đối tượng Attachment từ đường dẫn file
                    $attachments[] = Attachment::fromPath($path);
                } catch (\Exception $e) {
                    // Ghi log nếu có lỗi khi tạo attachment
                    Log::error("DynamicMailable: Lỗi khi tạo file đính kèm từ đường dẫn: " . $path . " - Lỗi: " . $e->getMessage());
                }
            } else {
                // Ghi log nếu đường dẫn file không hợp lệ hoặc file không tồn tại/không đọc được
                Log::warning("DynamicMailable: Đường dẫn file đính kèm không hợp lệ, không tìm thấy hoặc không thể đọc: " . print_r($path, true));
            }
        }
        return $attachments;
    }
}
