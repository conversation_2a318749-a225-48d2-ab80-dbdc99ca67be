<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\ProcessConditionService;
use Carbon\Carbon;

class ProcessConditionServiceEdgeCasesTest extends TestCase
{
    private ProcessConditionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ProcessConditionService();
    }

    /**
     * Test edge cases cho EQUAL operator
     */
    public function test_equal_edge_cases()
    {
        // Null values
        $this->assertTrue($this->evaluateCondition('equal', null, null));
        $this->assertTrue($this->evaluateCondition('equal', null, '')); // null == '' is true in PHP
        $this->assertTrue($this->evaluateCondition('equal', null, 0)); // null == 0 is true in PHP
        
        // Boolean values
        $this->assertTrue($this->evaluateCondition('equal', true, 1));
        $this->assertTrue($this->evaluateCondition('equal', false, 0));
        $this->assertTrue($this->evaluateCondition('equal', false, ''));
        
        // Array comparisons
        $this->assertTrue($this->evaluateCondition('equal', [1, 2, 3], [1, 2, 3]));
        $this->assertFalse($this->evaluateCondition('equal', [1, 2, 3], [3, 2, 1]));
        
        // Mixed types
        $this->assertTrue($this->evaluateCondition('equal', '123', 123));
        $this->assertTrue($this->evaluateCondition('equal', '0', false));
    }

    /**
     * Test edge cases cho IN operator với mảng lớn
     */
    public function test_in_operator_large_arrays()
    {
        // Mảng lớn
        $largeArray = range(1, 1000);
        $this->assertTrue($this->evaluateCondition('in', 500, $largeArray));
        $this->assertFalse($this->evaluateCondition('in', 1001, $largeArray));
        
        // actualValue là mảng lớn
        $actualArray = range(1, 100);
        $allowedArray = range(1, 200);
        $this->assertTrue($this->evaluateCondition('in', $actualArray, $allowedArray));
        
        // Một phần tử không có trong allowed
        $actualArray = range(1, 100);
        $actualArray[] = 300; // Thêm phần tử không có trong allowed
        $this->assertFalse($this->evaluateCondition('in', $actualArray, $allowedArray));
    }

    /**
     * Test edge cases cho CONTAIN operator
     */
    public function test_contain_edge_cases()
    {
        // Unicode strings
        $this->assertTrue($this->evaluateCondition('contain', 'Xin chào thế giới', 'chào'));
        $this->assertTrue($this->evaluateCondition('contain', 'Hello 世界', '世界'));
        
        // Special characters
        $this->assertTrue($this->evaluateCondition('contain', '<EMAIL>', '@'));
        $this->assertTrue($this->evaluateCondition('contain', 'price: $100', '$'));
        
        // Empty search term
        $this->assertTrue($this->evaluateCondition('contain', 'any string', ''));
        
        // Case sensitivity test
        $this->assertTrue($this->evaluateCondition('contain', 'Hello World', 'HELLO'));
        $this->assertTrue($this->evaluateCondition('contain', 'HELLO WORLD', 'hello'));
        
        // Mảng với mixed types
        $mixedArray = ['string', 123, true, null, 'contains_test'];
        $this->assertTrue($this->evaluateCondition('contain', $mixedArray, 'test'));
        $this->assertFalse($this->evaluateCondition('contain', $mixedArray, 'xyz'));
    }

    /**
     * Test edge cases cho BETWEEN operator
     */
    public function test_between_edge_cases()
    {
        // Boundary values
        $this->assertTrue($this->evaluateCondition('between', 0, [0, 10]));
        $this->assertTrue($this->evaluateCondition('between', 10, [0, 10]));
        
        // Negative numbers
        $this->assertTrue($this->evaluateCondition('between', -5, [-10, 0]));
        $this->assertFalse($this->evaluateCondition('between', 5, [-10, 0]));
        
        // Same start and end
        $this->assertTrue($this->evaluateCondition('between', 5, [5, 5]));
        $this->assertFalse($this->evaluateCondition('between', 4, [5, 5]));
        
        // Invalid date strings fallback to string comparison
        $this->assertTrue($this->evaluateCondition('between', 'invalid-date-b', ['invalid-date-a', 'invalid-date-c']));
        
        // Very large numbers
        $this->assertTrue($this->evaluateCondition('between', 999999999, [0, 1000000000]));
    }

    /**
     * Test edge cases cho EMPTY/NOT_EMPTY operators
     */
    public function test_empty_edge_cases()
    {
        // String với whitespace
        $this->assertTrue($this->evaluateCondition('empty', '   ', null));
        $this->assertTrue($this->evaluateCondition('empty', "\t\n\r", null));
        $this->assertFalse($this->evaluateCondition('empty', ' a ', null));
        
        // Array với null elements
        $this->assertFalse($this->evaluateCondition('empty', [null], null));
        $this->assertFalse($this->evaluateCondition('empty', [null, null], null));
        $this->assertTrue($this->evaluateCondition('empty', [], null));
        
        // Numeric edge cases
        $this->assertFalse($this->evaluateCondition('empty', 0, null));
        $this->assertFalse($this->evaluateCondition('empty', 0.0, null));
        $this->assertFalse($this->evaluateCondition('empty', -0, null));
        $this->assertFalse($this->evaluateCondition('empty', '0', null));
        
        // Object-like arrays
        $this->assertFalse($this->evaluateCondition('empty', ['key' => 'value'], null));
        $this->assertFalse($this->evaluateCondition('empty', ['key' => null], null));
    }

    /**
     * Test performance với dữ liệu lớn
     */
    public function test_performance_large_data()
    {
        $startTime = microtime(true);
        
        // Test với mảng rất lớn
        $largeArray = range(1, 10000);
        $searchArray = range(5000, 5100);
        
        $result = $this->evaluateCondition('in', $searchArray, $largeArray);
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        $this->assertTrue($result);
        $this->assertLessThan(1.0, $executionTime, 'Performance test: should complete within 1 second');
    }

    /**
     * Test với date formats khác nhau
     */
    public function test_date_formats()
    {
        // ISO format
        $this->assertTrue($this->evaluateCondition('greater', '2024-01-02T10:00:00Z', '2024-01-01T10:00:00Z'));
        
        // Different formats
        $this->assertTrue($this->evaluateCondition('greater', '02/01/2024', '01/01/2024'));
        $this->assertTrue($this->evaluateCondition('greater', '2024-01-02 10:00:00', '2024-01-01 10:00:00'));
        
        // Invalid dates fallback to string comparison
        $this->assertTrue($this->evaluateCondition('greater', 'not-a-date-z', 'not-a-date-a'));
    }

    /**
     * Test với mixed data types trong mảng
     */
    public function test_mixed_array_types()
    {
        $mixedArray = [1, '2', 3.0, true, false, null, 'string'];
        
        // IN operator với mixed types
        $this->assertTrue($this->evaluateCondition('in', [1, '2'], $mixedArray));
        $this->assertTrue($this->evaluateCondition('in', [true, false], $mixedArray));
        $this->assertTrue($this->evaluateCondition('in', ['not_exist'], $mixedArray)); // 'not_exist' == null (loose comparison)
        
        // CONTAIN operator với mixed array
        $this->assertTrue($this->evaluateCondition('contain', $mixedArray, 'str'));
        $this->assertFalse($this->evaluateCondition('contain', $mixedArray, 'xyz'));
    }

    /**
     * Test với parameter structures phức tạp
     */
    public function test_complex_parameter_structures()
    {
        // Nested arrays
        $complexParam = [
            ['value' => 'option1', 'label' => 'Option 1'],
            ['value' => 'option2', 'label' => 'Option 2'],
            ['value' => 'option3', 'label' => 'Option 3']
        ];
        
        $this->assertTrue($this->evaluateCondition('in', 'option2', $complexParam));
        $this->assertFalse($this->evaluateCondition('in', 'option4', $complexParam));
        
        // Array values with complex structure
        $this->assertTrue($this->evaluateCondition('in', ['option1', 'option3'], $complexParam));
        $this->assertFalse($this->evaluateCondition('in', ['option1', 'option4'], $complexParam));
    }

    /**
     * Test memory usage với dữ liệu lớn
     */
    public function test_memory_usage()
    {
        $initialMemory = memory_get_usage();
        
        // Tạo dữ liệu lớn
        $largeData = array_fill(0, 50000, 'test_string_data_for_memory_test');
        
        // Thực hiện nhiều operations
        for ($i = 0; $i < 100; $i++) {
            $this->evaluateCondition('in', 'test_string_data_for_memory_test', $largeData);
            $this->evaluateCondition('contain', $largeData, 'test');
            $this->evaluateCondition('not_empty', $largeData, null);
        }
        
        $finalMemory = memory_get_usage();
        $memoryIncrease = $finalMemory - $initialMemory;
        
        // Memory increase should be reasonable (less than 50MB)
        $this->assertLessThan(50 * 1024 * 1024, $memoryIncrease, 'Memory usage should be reasonable');
    }

    /**
     * Helper method để gọi evaluateSingleCondition
     */
    private function evaluateCondition(string $operator, $actualValue, $parameter): bool
    {
        $condition = [
            'f' => 'test_field',
            'o' => $operator,
            'p' => $parameter
        ];
        
        $dataSource = ['test_field' => $actualValue];
        
        return $this->service->evaluateSingleCondition($condition, $dataSource, 'form');
    }
}
