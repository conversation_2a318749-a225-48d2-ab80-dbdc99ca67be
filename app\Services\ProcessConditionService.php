<?php

namespace App\Services;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ProcessConditionService
{
    /**
     * Kiểm tra điều kiện từ các filter rules
     */
    public function checkCondition($field_values, $processInstance, $or_conditions, $user): bool 
    {
        $filterRules = $or_conditions;
        $formData = $field_values;
        
        // Chuẩn bị Context Data cho Service
        $contextData = [
            'user' => $user,
            'process' => $processInstance
        ];

        // Kiểm tra điều kiện
        $isValid = $this->checkAllFilterConditions(
            $filterRules,
            $formData,
            $contextData
        );

        return $isValid;
    }

    /**
     * Kiểm tra tất cả các filter condition
     */
    public function checkAllFilterConditions(array $filterRules, array $formData, $contextData = null): bool
    {
        if (empty($filterRules)) {
            return true; // Không có quy tắc, mặc định là hợp lệ
        }

        $overallResult = false; // Sẽ bị ghi đè bởi kết quả của khối đầu tiên
        $isFirstBlock = true;
        foreach ($filterRules as $index => $ruleGroup) {
            $objectType = $ruleGroup['object'] ?? null;
            $filterBlock = $ruleGroup['filter'] ?? null;
            $operatorBetweenBlocks = strtolower($ruleGroup['logicalOperator'] ?? 'and'); // Mặc định là AND

            // --- Xác thực cấu trúc cơ bản ---
            if (
                !$objectType
                || !$filterBlock
                || !isset($filterBlock['ors'])
                || !is_array($filterBlock['ors'])
            ) {
                // Cấu trúc sai -> Toàn bộ không hợp lệ
                return false;
            }
            
            // --- Lấy nguồn dữ liệu phù hợp ---
            $dataSource = null;
            if ($objectType === 'form') {
                $dataSource = $formData;
            } elseif ($objectType === 'process') {
                if ($contextData === null) {
                    // Thiếu context cho process -> Không hợp lệ
                    return false;
                }
                $dataSource = $contextData; // Nguồn là mảng context ['user'=>..., 'process'=>...]
            } else {
                // Object không hỗ trợ -> Không hợp lệ
                return false;
            }

            // --- Đánh giá logic AND bên trong khối hiện tại ---
            $conditions = $filterBlock['ors'];
            $currentBlockResult = $this->evaluateInternalBlockAndLogic($conditions, $dataSource, $objectType);
            
            // --- Kết hợp kết quả khối này với kết quả tổng thể ---
            if ($isFirstBlock) {
                $overallResult = $currentBlockResult;
                $isFirstBlock = false;
            } else {
                // Áp dụng toán tử logic kết nối của khối hiện tại
                if ($operatorBetweenBlocks === 'and') {
                    $overallResult = $overallResult && $currentBlockResult;
                } elseif ($operatorBetweenBlocks === 'or') {
                    $overallResult = $overallResult || $currentBlockResult;
                } else {
                    $overallResult = $overallResult && $currentBlockResult; // Mặc định dùng AND nếu toán tử lạ
                }
            }
        }
        
        return $overallResult;
    }

    /**
     * Đánh giá logic AND trong một block
     */
    public function evaluateInternalBlockAndLogic(array $conditions, $dataSource, string $objectType): bool
    {
        // Khối không có điều kiện thì luôn đúng với logic AND
        if (empty($conditions)) {
            return true;
        }
        
        foreach ($conditions as $condition) {
            if (!$this->evaluateSingleCondition($condition, $dataSource, $objectType)) {
                // Một điều kiện sai -> Cả khối sai
                return false;
            }
        }
        // Tất cả điều kiện đều đúng
        return true;
    }

    /**
     * Đánh giá một điều kiện đơn
     */
    public function evaluateSingleCondition(array $condition, $dataSource, string $objectType): bool
    {
        $field = $condition['f'] ?? null; // Tên trường logic
        $operator = $condition['o'] ?? null; // Toán tử so sánh
        $parameter = $condition['p'] ?? null; // Giá trị để so sánh

        // Chuẩn hóa operator thành chữ thường để dễ so sánh
        $operator = $operator ? strtolower($operator) : null;

        if (!$field || !$operator) {
            return false;
        }
        
        // Lấy giá trị thực tế từ nguồn dữ liệu tương ứng
        $actualValue = null;
        if ($objectType === 'form') {
            $actualValue = data_get($dataSource, $field);
        } elseif ($objectType === 'process') {
            $actualValue = $this->getContextValue($dataSource, $field);
        } else {
            // Trường hợp này không nên xảy ra nếu check ở hàm chính
            return false;
        }
        
        // Thực hiện so sánh dựa trên operator
        switch ($operator) {
            case config('constants.operator.EQUAL'):
                // 1. Kiểm tra bằng nghiêm ngặt (===) trước tiên.
                if ($actualValue === $parameter) {
                    return true;
                }
                // 2. Xử lý trường hợp cả hai đều là số (hoặc chuỗi số).
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return $actualValue == $parameter;
                }
                // 3. Mặc định cuối cùng: So sánh lỏng lẻo (==)
                return $actualValue == $parameter;
            case config('constants.operator.NOT_EQUAL'):
                // 1. Kiểm tra không bằng nghiêm ngặt (!==). Nếu kiểu hoặc giá trị khác nhau -> true.
                if ($actualValue === $parameter) {
                    return false; // Nếu bằng nghiêm ngặt thì không phải là 'not_equal'
                }
                // 2. Xử lý trường hợp cả hai đều là số (hoặc chuỗi số).
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    // So sánh không bằng sau khi biết cả hai là số.
                    return $actualValue != $parameter;
                }
                // 3. Mặc định cuối cùng: So sánh lỏng lẻo (==)
                return $actualValue != $parameter;
            case config('constants.operator.GREATER'): // Lớn hơn (>)
                // Xử lý trường hợp $actualValue là mảng
                if (is_array($actualValue)) {
                    return false; // Mảng không thể so sánh lớn hơn
                }

                // Ưu tiên 1: So sánh số (nguyên và thực)
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return (float) $actualValue > (float) $parameter;
                }

                // Ưu tiên 2: So sánh chuỗi thông thường trước
                if (is_string($actualValue) && is_string($parameter)) {
                    // Chỉ thử parse ngày nếu chuỗi có vẻ như ngày (chứa -, /, :, space)
                    if (!is_numeric($actualValue) && !is_numeric($parameter) &&
                        (preg_match('/\d{4}-\d{2}-\d{2}/', $actualValue) || preg_match('/\d{1,2}\/\d{1,2}\/\d{4}/', $actualValue) ||
                         preg_match('/\d{4}-\d{2}-\d{2}/', $parameter) || preg_match('/\d{1,2}\/\d{1,2}\/\d{4}/', $parameter))) {
                        try {
                            $dateActual = Carbon::parse($actualValue);
                            $dateParam = Carbon::parse($parameter);
                            return $dateActual->greaterThan($dateParam);
                        } catch (\Exception $e) {
                            // Nếu không parse được ngày, so sánh chuỗi
                            return strcmp($actualValue, $parameter) > 0;
                        }
                    } else {
                        // So sánh chuỗi thông thường
                        return strcmp($actualValue, $parameter) > 0;
                    }
                }

                return false;
            case config('constants.operator.LOWER'): // Nhỏ hơn (<)
                // Xử lý trường hợp $actualValue là mảng
                if (is_array($actualValue)) {
                    return false; // Mảng không thể so sánh nhỏ hơn
                }

                // Ưu tiên 1: So sánh số
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return (float) $actualValue < (float) $parameter;
                }

                // Ưu tiên 2: So sánh chuỗi thông thường trước
                if (is_string($actualValue) && is_string($parameter)) {
                    // Chỉ thử parse ngày nếu chuỗi có vẻ như ngày (chứa -, /, :, space)
                    if (!is_numeric($actualValue) && !is_numeric($parameter) &&
                        (preg_match('/\d{4}-\d{2}-\d{2}/', $actualValue) || preg_match('/\d{1,2}\/\d{1,2}\/\d{4}/', $actualValue) ||
                         preg_match('/\d{4}-\d{2}-\d{2}/', $parameter) || preg_match('/\d{1,2}\/\d{1,2}\/\d{4}/', $parameter))) {
                        try {
                            $dateActual = Carbon::parse($actualValue);
                            $dateParam = Carbon::parse($parameter);
                            return $dateActual->lessThan($dateParam);
                        } catch (\Exception $e) {
                            // Nếu không parse được ngày, so sánh chuỗi
                            return strcmp($actualValue, $parameter) < 0;
                        }
                    } else {
                        // So sánh chuỗi thông thường
                        return strcmp($actualValue, $parameter) < 0;
                    }
                }

                return false;
            case config('constants.operator.NOT_GREATER'): // Nhỏ hơn hoặc bằng (<=)
                // Xử lý trường hợp $actualValue là mảng
                if (is_array($actualValue)) {
                    return false; // Mảng không thể so sánh nhỏ hơn hoặc bằng
                }

                // Ưu tiên 1: So sánh số
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return (float) $actualValue <= (float) $parameter;
                }

                // Ưu tiên 2: So sánh ngày/giờ/thời gian
                if (is_string($actualValue) && !empty($actualValue) && !is_numeric($actualValue) &&
                    is_string($parameter) && !empty($parameter) && !is_numeric($parameter))
                {
                    try {
                        $dateActual = Carbon::parse($actualValue);
                        $dateParam = Carbon::parse($parameter);
                        return $dateActual->lessThanOrEqualTo($dateParam);
                    } catch (\Exception $e) {
                        // Nếu không parse được ngày, thử so sánh chuỗi
                        return strcmp($actualValue, $parameter) <= 0;
                    }
                }

                // Ưu tiên 3: So sánh chuỗi thông thường
                if (is_string($actualValue) && is_string($parameter)) {
                    return strcmp($actualValue, $parameter) <= 0;
                }

                return false;
            case config('constants.operator.NOT_LOWER'): // Lớn hơn hoặc bằng (>=)
                // Xử lý trường hợp $actualValue là mảng
                if (is_array($actualValue)) {
                    return false; // Mảng không thể so sánh lớn hơn hoặc bằng
                }

                // Ưu tiên 1: So sánh số
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return (float) $actualValue >= (float) $parameter;
                }

                // Ưu tiên 2: So sánh ngày/giờ/thời gian
                if (is_string($actualValue) && !empty($actualValue) && !is_numeric($actualValue) &&
                    is_string($parameter) && !empty($parameter) && !is_numeric($parameter))
                {
                    try {
                        $dateActual = Carbon::parse($actualValue);
                        $dateParam = Carbon::parse($parameter);
                        return $dateActual->greaterThanOrEqualTo($dateParam);
                    } catch (\Exception $e) {
                        // Nếu không parse được ngày, thử so sánh chuỗi
                        return strcmp($actualValue, $parameter) >= 0;
                    }
                }

                // Ưu tiên 3: So sánh chuỗi thông thường
                if (is_string($actualValue) && is_string($parameter)) {
                    return strcmp($actualValue, $parameter) >= 0;
                }

                return false;
            case config('constants.operator.IN'): // Nằm trong (Thuộc)
                // Xử lý $allowedValues từ $parameter
                $allowedValues = [];
                if (is_array($parameter)) {
                    // Parameter là array
                    if (isset($parameter[0]) && is_array($parameter[0]) && isset($parameter[0]['value'])) {
                        $allowedValues = array_column($parameter, 'value');
                    } else {
                        $allowedValues = $parameter;
                    }
                } elseif (is_string($parameter)) {
                    // Parameter là string - chuyển thành array với 1 phần tử
                    $allowedValues = [$parameter];
                } else {
                    // Parameter là giá trị đơn khác (number, boolean, etc.)
                    $allowedValues = [$parameter];
                }

                // Trường hợp $actualValue là mảng - đưa về bài toán kiểm tra nằm trong
                if (is_array($actualValue)) {
                    // Kiểm tra từng phần tử trong $actualValue có nằm trong $allowedValues không
                    foreach ($actualValue as $value) {
                        if (!in_array($value, $allowedValues)) {
                            return false;
                        }
                    }
                    return true;
                }
                // Trường hợp $actualValue là giá trị đơn
                return in_array($actualValue, $allowedValues);
            case config('constants.operator.NOT_IN'): // Không nằm trong (Không thuộc)
                // Xử lý $disallowedValues từ $parameter
                $disallowedValues = [];
                if (is_array($parameter)) {
                    // Parameter là array
                    if (isset($parameter[0]) && is_array($parameter[0]) && isset($parameter[0]['value'])) {
                        $disallowedValues = array_column($parameter, 'value');
                    } else {
                        $disallowedValues = $parameter;
                    }
                } elseif (is_string($parameter)) {
                    // Parameter là string - chuyển thành array với 1 phần tử
                    $disallowedValues = [$parameter];
                } else {
                    // Parameter là giá trị đơn khác (number, boolean, etc.)
                    $disallowedValues = [$parameter];
                }

                // Trường hợp $actualValue là mảng - đưa về bài toán kiểm tra nằm trong
                if (is_array($actualValue)) {
                    // Kiểm tra có phần tử nào trong $actualValue nằm trong $disallowedValues không
                    foreach ($actualValue as $value) {
                        if (in_array($value, $disallowedValues)) {
                            return false; // Nếu có phần tử nào nằm trong danh sách cấm thì return false
                        }
                    }
                    return true; // Nếu không có phần tử nào nằm trong danh sách cấm
                }
                // Trường hợp $actualValue là giá trị đơn
                return !in_array($actualValue, $disallowedValues);
            case config('constants.operator.CONTAIN'): // Chứa cụm từ
                if (!is_string($parameter)) {
                    return false;
                }

                // Empty string luôn được chứa trong bất kỳ chuỗi nào
                if ($parameter === '') {
                    return true;
                }

                // Xử lý trường hợp $actualValue là mảng
                if (is_array($actualValue)) {
                    // Kiểm tra có phần tử nào trong mảng chứa cụm từ không
                    foreach ($actualValue as $value) {
                        if (is_string($value) && !empty($value) && Str::contains($value, $parameter, true)) {
                            return true;
                        }
                    }
                    return false;
                }

                // Xử lý trường hợp $actualValue là chuỗi
                if (!is_string($actualValue) || $actualValue === '') {
                    return false;
                }

                return Str::contains($actualValue, $parameter, true); // true = ignore case (case insensitive)
            case config('constants.operator.NOT_CONTAIN'): // Không chứa cụm từ
                if (!is_string($parameter)) {
                    return true;
                }

                // Xử lý trường hợp $actualValue là mảng
                if (is_array($actualValue)) {
                    // Kiểm tra không có phần tử nào trong mảng chứa cụm từ
                    foreach ($actualValue as $value) {
                        if (is_string($value) && !empty($value) && Str::contains($value, $parameter, true)) {
                            return false; // Nếu có phần tử chứa cụm từ thì return false
                        }
                    }
                    return true; // Không có phần tử nào chứa cụm từ
                }

                // Xử lý trường hợp $actualValue là chuỗi
                if (!is_string($actualValue) || $actualValue === '') {
                    return true; // Chuỗi rỗng không chứa gì
                }

                return !Str::contains($actualValue, $parameter, true); // true = ignore case (case insensitive)
            case config('constants.operator.EMPTY'): // Trống
                // Xử lý cụ thể từng loại dữ liệu
                if (is_array($actualValue)) {
                    return count($actualValue) === 0;
                }
                if (is_string($actualValue)) {
                    return trim($actualValue) === '';
                }
                if (is_null($actualValue)) {
                    return true;
                }
                if (is_numeric($actualValue)) {
                    return false; // Số không bao giờ được coi là "empty"
                }
                return empty($actualValue);
            case config('constants.operator.NOT_EMPTY'): // Không trống
                // Xử lý cụ thể từng loại dữ liệu
                if (is_array($actualValue)) {
                    return count($actualValue) > 0;
                }
                if (is_string($actualValue)) {
                    return trim($actualValue) !== '';
                }
                if (is_null($actualValue)) {
                    return false;
                }
                if (is_numeric($actualValue)) {
                    return true; // Số luôn được coi là "not empty"
                }
                return !empty($actualValue);
            case config('constants.operator.BETWEEN'): // Thuộc khoảng
                // Validate parameter structure
                if (!is_array($parameter) || count($parameter) !== 2) {
                    return false;
                }

                // Xử lý trường hợp $actualValue là mảng
                if (is_array($actualValue)) {
                    return false; // Mảng không thể kiểm tra "between"
                }

                [$startParam, $endParam] = array_values($parameter);

                // So sánh số
                if (is_numeric($actualValue) && is_numeric($startParam) && is_numeric($endParam)) {
                    $val = (float)$actualValue;
                    $min = min((float)$startParam, (float)$endParam);
                    $max = max((float)$startParam, (float)$endParam);
                    return $val >= $min && $val <= $max;
                }

                // So sánh ngày/thời gian
                if (is_string($actualValue) && !empty($actualValue) && !is_numeric($actualValue) &&
                    is_string($startParam) && !empty($startParam) && !is_numeric($startParam) &&
                    is_string($endParam) && !empty($endParam) && !is_numeric($endParam))
                {
                    try {
                        $dateActual = Carbon::parse($actualValue);
                        $dateStart = Carbon::parse($startParam);
                        $dateEnd = Carbon::parse($endParam);
                        return $dateActual->between($dateStart, $dateEnd, true);
                    } catch (\Exception $e) {
                        // Nếu không parse được ngày, thử so sánh chuỗi
                        $cmpStart = strcmp($actualValue, $startParam);
                        $cmpEnd = strcmp($actualValue, $endParam);
                        return $cmpStart >= 0 && $cmpEnd <= 0;
                    }
                }

                return false;
            case config('constants.operator.OTHER'): // Khác
                // Xử lý parameter có cấu trúc đặc biệt
                if (is_array($parameter) && isset($parameter['value']) && !isset($parameter[0])) {
                    $parameter = $parameter['value'];
                }

                // Xử lý trường hợp $actualValue là mảng
                if (is_array($actualValue)) {
                    // So sánh mảng với giá trị đơn - luôn khác nhau
                    if (!is_array($parameter)) {
                        return true;
                    }
                    // So sánh hai mảng
                    return $actualValue !== $parameter;
                }

                // Kiểm tra bằng nghiêm ngặt trước
                if ($actualValue === $parameter) {
                    return false;
                }

                // So sánh số
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return (float)$actualValue !== (float)$parameter;
                }

                // So sánh ngày/thời gian
                if (is_string($actualValue) && !empty($actualValue) && !is_numeric($actualValue) &&
                    is_string($parameter) && !empty($parameter) && !is_numeric($parameter))
                {
                    try {
                        $dateActual = Carbon::parse($actualValue);
                        $dateParam = Carbon::parse($parameter);
                        return !$dateActual->equalTo($dateParam);
                    } catch (\Exception $e) {
                        // Nếu không parse được ngày, so sánh chuỗi thông thường
                        return $actualValue !== $parameter;
                    }
                }

                // So sánh lỏng lẻo cuối cùng
                return $actualValue != $parameter;
            default:
                return false;
        }
    }

    /**
     * Lấy giá trị từ context
     */
    public function getContextValue(array $contextData, string $logicalFieldName)
    {
        if (empty($contextData['user']) || empty($contextData['process'])) {
            return null;
        }
        $user = $contextData['user'];
        $process = $contextData['process'];

        $value = null;
        $targetObject = null;
        $actualFieldName = null;

        // Các trường kiểm tra theo User hiện tại
        $userCheckFields = ['department_ids', 'job_position_ids', 'rank_ids'];
        if (in_array($logicalFieldName, $userCheckFields)) {
            $targetObject = $user;
            $userFieldMapping = [
                'department_ids' => 'department_id',
                'job_position_ids' => 'position_id',
                'rank_ids' => 'rank_id',
            ];
            $actualFieldName = $userFieldMapping[$logicalFieldName] ?? null;
        }
        
        // Các trường kiểm tra theo bản ghi Process hiện tại
        $processCheckFields = ['name', 'description', 'created_by'];
        if (in_array($logicalFieldName, $processCheckFields)) {
            $targetObject = $process;
            $actualFieldName = $logicalFieldName;
        }

        // Nếu không thuộc loại nào ở trên
        if ($targetObject === null || $actualFieldName === null) {
            return null;
        }

        try {
            if ($targetObject instanceof Model) {
                $value = $targetObject->{$actualFieldName} ?? null;
            } elseif (is_object($targetObject)) {
                $value = $targetObject->{$actualFieldName} ?? null;
            } elseif (is_array($targetObject)) {
                $value = Arr::get($targetObject, $actualFieldName);
            }
        } catch (\Throwable $e) {
            $value = null;
        }

        return $value;
    }
} 