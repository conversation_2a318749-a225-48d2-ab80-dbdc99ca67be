<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\ProcessConditionService;

class ProcessConditionServiceStringParameterTest extends TestCase
{
    private ProcessConditionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ProcessConditionService();
    }

    /**
     * Test IN operator với parameter là string
     */
    public function test_in_operator_with_string_parameter()
    {
        // Parameter là string đơn
        $this->assertTrue($this->evaluateCondition('in', 'apple', 'apple'));
        $this->assertFalse($this->evaluateCondition('in', 'banana', 'apple'));
        
        // actualValue là mảng, parameter là string
        $this->assertTrue($this->evaluateCondition('in', ['apple'], 'apple'));
        $this->assertFalse($this->evaluateCondition('in', ['apple', 'banana'], 'apple')); // banana không có trong ['apple']
        
        // Parameter là số
        $this->assertTrue($this->evaluateCondition('in', 123, 123));
        $this->assertTrue($this->evaluateCondition('in', [123], 123));
        $this->assertFalse($this->evaluateCondition('in', 456, 123));
        
        // Parameter là boolean
        $this->assertTrue($this->evaluateCondition('in', true, true));
        $this->assertTrue($this->evaluateCondition('in', [true], true));
        $this->assertFalse($this->evaluateCondition('in', false, true));
    }

    /**
     * Test NOT_IN operator với parameter là string
     */
    public function test_not_in_operator_with_string_parameter()
    {
        // Parameter là string đơn
        $this->assertFalse($this->evaluateCondition('not_in', 'apple', 'apple'));
        $this->assertTrue($this->evaluateCondition('not_in', 'banana', 'apple'));
        
        // actualValue là mảng, parameter là string
        $this->assertFalse($this->evaluateCondition('not_in', ['apple'], 'apple'));
        $this->assertTrue($this->evaluateCondition('not_in', ['banana'], 'apple'));
        $this->assertFalse($this->evaluateCondition('not_in', ['apple', 'banana'], 'apple')); // apple có trong disallowed
        
        // Parameter là số
        $this->assertFalse($this->evaluateCondition('not_in', 123, 123));
        $this->assertTrue($this->evaluateCondition('not_in', 456, 123));
        
        // Parameter là boolean
        $this->assertFalse($this->evaluateCondition('not_in', true, true));
        $this->assertTrue($this->evaluateCondition('not_in', false, true));
    }

    /**
     * Test IN operator với parameter null
     */
    public function test_in_operator_with_null_parameter()
    {
        $this->assertTrue($this->evaluateCondition('in', null, null));
        $this->assertFalse($this->evaluateCondition('in', 'test', null));
        $this->assertTrue($this->evaluateCondition('in', [null], null));
        $this->assertFalse($this->evaluateCondition('in', [null, 'test'], null)); // 'test' không có trong [null]
    }

    /**
     * Test NOT_IN operator với parameter null
     */
    public function test_not_in_operator_with_null_parameter()
    {
        $this->assertFalse($this->evaluateCondition('not_in', null, null));
        $this->assertTrue($this->evaluateCondition('not_in', 'test', null));
        $this->assertFalse($this->evaluateCondition('not_in', [null], null));
        $this->assertTrue($this->evaluateCondition('not_in', ['test'], null));
    }

    /**
     * Test IN operator với mixed parameter types
     */
    public function test_in_operator_mixed_parameter_types()
    {
        // String vs Number (loose comparison)
        $this->assertTrue($this->evaluateCondition('in', '123', 123)); // '123' == 123
        $this->assertTrue($this->evaluateCondition('in', 123, '123')); // 123 == '123'
        
        // Boolean vs Number (loose comparison)
        $this->assertTrue($this->evaluateCondition('in', true, 1)); // true == 1
        $this->assertTrue($this->evaluateCondition('in', false, 0)); // false == 0
        
        // Array with mixed types - tất cả phần tử phải nằm trong parameter
        $this->assertTrue($this->evaluateCondition('in', ['123'], 123)); // '123' == 123
        $this->assertFalse($this->evaluateCondition('in', ['123', 456], 123)); // 456 không có trong [123]
        $this->assertFalse($this->evaluateCondition('in', ['abc', 456], 123));
    }

    /**
     * Test NOT_IN operator với mixed parameter types
     */
    public function test_not_in_operator_mixed_parameter_types()
    {
        // String vs Number (loose comparison)
        $this->assertFalse($this->evaluateCondition('not_in', '123', 123)); // '123' == 123
        $this->assertFalse($this->evaluateCondition('not_in', 123, '123')); // 123 == '123'
        
        // Boolean vs Number (loose comparison)
        $this->assertFalse($this->evaluateCondition('not_in', true, 1)); // true == 1
        $this->assertFalse($this->evaluateCondition('not_in', false, 0)); // false == 0
        
        // Array with mixed types - nếu có bất kỳ phần tử nào trong disallowed thì false
        $this->assertFalse($this->evaluateCondition('not_in', ['123'], 123)); // '123' == 123
        $this->assertFalse($this->evaluateCondition('not_in', ['123', 456], 123)); // '123' có trong disallowed
        $this->assertTrue($this->evaluateCondition('not_in', ['abc', 456], 123)); // không có phần tử nào == 123
    }

    /**
     * Test edge cases với empty values
     */
    public function test_in_not_in_with_empty_values()
    {
        // Empty string
        $this->assertTrue($this->evaluateCondition('in', '', ''));
        $this->assertFalse($this->evaluateCondition('in', 'test', ''));
        $this->assertTrue($this->evaluateCondition('in', [''], ''));
        
        // Empty array as actualValue with string parameter
        $this->assertTrue($this->evaluateCondition('in', [], 'anything')); // Empty array always returns true for IN
        $this->assertTrue($this->evaluateCondition('not_in', [], 'anything')); // Empty array always returns true for NOT_IN
    }

    /**
     * Test performance với string parameters
     */
    public function test_performance_with_string_parameters()
    {
        $startTime = microtime(true);
        
        // Test nhiều operations với string parameters
        for ($i = 0; $i < 1000; $i++) {
            $this->evaluateCondition('in', "test_{$i}", "test_500");
            $this->evaluateCondition('not_in', "test_{$i}", "test_500");
        }
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        $this->assertLessThan(1.0, $executionTime, 'String parameter operations should be fast');
    }

    /**
     * Helper method để gọi evaluateSingleCondition
     */
    private function evaluateCondition(string $operator, $actualValue, $parameter): bool
    {
        $condition = [
            'f' => 'test_field',
            'o' => $operator,
            'p' => $parameter
        ];
        
        $dataSource = ['test_field' => $actualValue];
        
        return $this->service->evaluateSingleCondition($condition, $dataSource, 'form');
    }
}
