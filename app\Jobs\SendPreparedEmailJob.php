<?php

namespace App\Jobs;

use App\Mail\DynamicMailable; // Import Mailable đã tạo
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue; // Quan trọng: Job này sẽ được đưa vào hàng đợi
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Throwable; // Sử dụng Throwable để bắt tất cả các loại lỗi

class SendPreparedEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    // Dữ liệu cần thiết cho Job
    protected array $toEmails;          // Mảng các địa chỉ email người nhận chính (đã được phân giải)
    protected ?array $ccEmails;         // Mảng các địa chỉ email CC (đã được phân giải, có thể null)
    protected ?array $bccEmails;        // Mảng các địa chỉ email BCC (đã được phân giải, có thể null)
    protected string $subjectTemplate;   // Template tiêu đề email (còn chứa placeholder)
    protected string $contentTemplate;   // Template nội dung email (còn chứa placeholder)
    protected array $replacementData;   // Dữ liệu dùng để thay thế các placeholder
    protected array $attachmentPaths;   // Mảng các đường dẫn tuyệt đối tới file đính kèm (đã được chuẩn bị)
    protected ?string $fromAddress;     // Địa chỉ email "From" cuối cùng (đã được xác định)
    protected ?string $fromName;        // Tên người gửi "From" cuối cùng (đã được xác định)
    protected ?string $mailTemplateId;  // ID của template mail gốc (dùng để log, tùy chọn)
    protected ?string $jobDetailUrl;    // URL chi tiết công việc để người dùng click vào

    public $tries = 3; //Số lần Job sẽ được thử lại nếu thất bại.
    public $backoff = [60, 120, 300]; // Thử lại sau 1 phút, rồi 2 phút, rồi 5 phút
    public function __construct(
        array $toEmails,
        ?array $ccEmails,
        ?array $bccEmails,
        string $subjectTemplate,
        string $contentTemplate,
        array $replacementData,
        array $attachmentPaths,
        ?string $fromAddress,
        ?string $fromName,
        ?string $mailTemplateId = null,
        ?string $jobDetailUrl = null
    ) {
        $this->toEmails = $toEmails;
        $this->ccEmails = $ccEmails;
        $this->bccEmails = $bccEmails;
        $this->subjectTemplate = $subjectTemplate;
        $this->contentTemplate = $contentTemplate;
        $this->replacementData = $replacementData;
        $this->attachmentPaths = $attachmentPaths;
        $this->fromAddress = $fromAddress;
        $this->fromName = $fromName;
        $this->mailTemplateId = $mailTemplateId;
        $this->jobDetailUrl = $jobDetailUrl;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Kiểm tra lại xem có người nhận chính không
        if (empty($this->toEmails)) {
            Log::warning('SendDynamicEmailJob: Không có người nhận "To" nào được cung cấp. Bỏ qua Job.', ['template_id' => $this->mailTemplateId]);
            return; // Kết thúc Job nếu không có người nhận
        }

        try {
            // 1. Thay thế các placeholder trong tiêu đề và nội dung TRƯỚC khi escape HTML
            $finalSubject = $this->replacePlaceholders($this->subjectTemplate, $this->replacementData);
            // Thay thế placeholder trước, sau đó mới xử lý HTML entities và xuống dòng
            $contentWithPlaceholdersReplaced = $this->replacePlaceholders($this->contentTemplate, $this->replacementData);
            // Xử lý xuống dòng (nl2br) và HTML entities (htmlspecialchars) cho nội dung SAU khi thay thế
            // Điều này giúp đảm bảo placeholder được thay thế đúng và nội dung hiển thị an toàn trong HTML email.
            $finalContent = nl2br(htmlspecialchars($contentWithPlaceholdersReplaced));

            // 2. Tạo một instance của DynamicMailable với dữ liệu đã được xử lý hoàn chỉnh
            $mailable = new DynamicMailable(
                $finalSubject,
                $finalContent,
                $this->attachmentPaths, // Truyền mảng đường dẫn file đã được chuẩn bị
                $this->fromAddress,     // Truyền địa chỉ "From" đã được xác định
                $this->fromName,        // Truyền tên người gửi "From" đã được xác định
                $this->jobDetailUrl     // Truyền URL chi tiết công việc
            );

            // 3. Xây dựng và gửi email
            $mailBuilder = Mail::to($this->toEmails);

            if (!empty($this->ccEmails)) {
                $mailBuilder->cc($this->ccEmails);
            }
            if (!empty($this->bccEmails)) {
                $mailBuilder->bcc($this->bccEmails);
            }

            $mailBuilder->send($mailable); // Gửi email

            // Ghi log thông báo gửi thành công (tùy chọn)
            Log::info('SendDynamicEmailJob: Email đã được gửi thành công.', [
                'template_id' => $this->mailTemplateId,
                'name_title' => $finalSubject,
                'to' => implode(', ', $this->toEmails),
            ]);

        } catch (Throwable $exception) { // Bắt Throwable để bao gồm cả Error và Exception
            // Ghi log chi tiết khi có lỗi xảy ra trong quá trình gửi
            Log::error('SendDynamicEmailJob: Lỗi khi gửi email.', [
                'template_id' => $this->mailTemplateId,
                'exception_message' => $exception->getMessage(),
                'exception_trace' => $exception->getTraceAsString(), // Ghi stack trace để debug
                'to_emails' => $this->toEmails,
                'replacement_data' => $this->replacementData, // Log dữ liệu có thể hữu ích
            ]);

            // Ném lại lỗi. Worker của Laravel sẽ dựa vào đây để biết Job thất bại,
            // sau đó sẽ thử lại (nếu $this->tries > 1 và số lần thử chưa hết)
            // hoặc gọi phương thức failed() nếu hết số lần thử.
            throw $exception;
        }
    }

    public function replacePlaceholders(string $text, array $variables): string
    {
        // Sử dụng preg_replace_callback để xử lý an toàn hơn và linh hoạt hơn
        // Nó chỉ thay thế nếu key tồn tại trong mảng $variables.
        return preg_replace_callback(
            '/\{([^{}]+)\}/', // Regex tìm các chuỗi dạng {key_name}
            function ($matches) use ($variables) {
                $key = trim($matches[1]); // Lấy key bên trong dấu {}, ví dụ: 'tu_ngay'
                $originalPlaceholder = $matches[0]; // Toàn bộ placeholder, ví dụ: {tu_ngay}
                // Log key đang được xử lý và kiểm tra sự tồn tại của nó trong mảng $variables
                $keyExists = array_key_exists($key, $variables);
                // Trả về giá trị nếu key tồn tại trong $variables, ngược lại trả về chính placeholder đó (không thay thế)
                return $keyExists ? (is_array($variables[$key]) ? implode(', ', $variables[$key]) : $variables[$key]) : $originalPlaceholder;
            }, $text);
    }

    public function failed(Throwable $exception): void
    {
        // dd($exception->getMessage());
        // Ghi log nghiêm trọng hoặc gửi thông báo cho quản trị viên khi Job thất bại hoàn toàn
        Log::critical('SendDynamicEmailJob: Job gửi email thất bại vĩnh viễn.', [
            'template_id' => $this->mailTemplateId,
            'to_emails' => implode(', ', $this->toEmails),
            'error_message' => $exception->getMessage(),
            // Bạn có thể thêm các thông tin khác nếu cần thiết cho việc debug
        ]);

        // Ví dụ: Gửi thông báo đến một kênh giám sát hoặc một email admin khác
        // CẢNH BÁO: Tránh gửi email ngay trong phương thức failed() nếu hệ thống mail chính đang gặp sự cố.
        // Cân nhắc sử dụng Notification của Laravel hoặc ghi vào một bảng lỗi riêng trong DB.
        /*
        try {
            Mail::raw(
                "Job SendDynamicEmailJob thất bại cho template ID: {$this->mailTemplateId}.\nNgười nhận: " . implode(', ', $this->toEmails) . "\nLỗi: {$exception->getMessage()}",
                function ($message) {
                    $message->to('<EMAIL>') // Thay bằng email admin của bạn
                            ->subject('Thông Báo Lỗi Job Gửi Email');
                }
            );
        } catch (Throwable $e) {
            Log::error('SendDynamicEmailJob: Không thể gửi email thông báo lỗi Job.', ['error' => $e->getMessage()]);
        }
        */
    }
}
