<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\ProcessConditionService;
use Carbon\Carbon;

class ProcessConditionServiceTest extends TestCase
{
    private ProcessConditionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ProcessConditionService();
    }

    /**
     * Test EQUAL operator với các trường hợp khác nhau
     */
    public function test_equal_operator()
    {
        // Test bằng nghiêm ngặt
        $this->assertTrue($this->evaluateCondition('equal', 'test', 'test'));
        $this->assertTrue($this->evaluateCondition('equal', 123, 123));
        $this->assertTrue($this->evaluateCondition('equal', true, true));

        // Test so sánh số
        $this->assertTrue($this->evaluateCondition('equal', '123', 123));
        $this->assertTrue($this->evaluateCondition('equal', 123.0, '123'));

        // Test so sánh lỏng lẻo
        $this->assertTrue($this->evaluateCondition('equal', '0', false));
        $this->assertFalse($this->evaluateCondition('equal', 'test', 'TEST'));
    }

    /**
     * Test NOT_EQUAL operator
     */
    public function test_not_equal_operator()
    {
        $this->assertFalse($this->evaluateCondition('not_equal', 'test', 'test'));
        $this->assertTrue($this->evaluateCondition('not_equal', 'test', 'TEST'));
        $this->assertFalse($this->evaluateCondition('not_equal', '123', 123));
        $this->assertTrue($this->evaluateCondition('not_equal', 'abc', 123));
    }

    /**
     * Test GREATER operator với số
     */
    public function test_greater_operator_numbers()
    {
        // So sánh số
        $this->assertTrue($this->evaluateCondition('greater', 10, 5));
        $this->assertTrue($this->evaluateCondition('greater', '10.5', 10));
        $this->assertFalse($this->evaluateCondition('greater', 5, 10));
        $this->assertFalse($this->evaluateCondition('greater', 10, 10));

        // Mảng không thể so sánh
        $this->assertFalse($this->evaluateCondition('greater', [1, 2, 3], 5));
    }

    /**
     * Test GREATER operator với ngày
     */
    public function test_greater_operator_dates()
    {
        $this->assertTrue($this->evaluateCondition('greater', '2024-01-02', '2024-01-01'));
        $this->assertFalse($this->evaluateCondition('greater', '2024-01-01', '2024-01-02'));
        
        // Test với format khác nhau
        $this->assertTrue($this->evaluateCondition('greater', '2024-01-02 10:00:00', '2024-01-02 09:00:00'));
    }

    /**
     * Test GREATER operator với chuỗi
     */
    public function test_greater_operator_strings()
    {
        $this->assertTrue($this->evaluateCondition('greater', 'b', 'a'));
        $this->assertFalse($this->evaluateCondition('greater', 'a', 'b'));
        $this->assertTrue($this->evaluateCondition('greater', 'abc', 'abb'));
    }

    /**
     * Test LOWER operator
     */
    public function test_lower_operator()
    {
        // Số
        $this->assertTrue($this->evaluateCondition('lower', 5, 10));
        $this->assertFalse($this->evaluateCondition('lower', 10, 5));
        
        // Ngày
        $this->assertTrue($this->evaluateCondition('lower', '2024-01-01', '2024-01-02'));
        
        // Chuỗi
        $this->assertTrue($this->evaluateCondition('lower', 'a', 'b'));
        
        // Mảng
        $this->assertFalse($this->evaluateCondition('lower', [1, 2], 5));
    }

    /**
     * Test NOT_GREATER operator (<=)
     */
    public function test_not_greater_operator()
    {
        $this->assertTrue($this->evaluateCondition('not_greater', 5, 10));
        $this->assertTrue($this->evaluateCondition('not_greater', 10, 10));
        $this->assertFalse($this->evaluateCondition('not_greater', 15, 10));
        
        // Mảng
        $this->assertFalse($this->evaluateCondition('not_greater', [1, 2], 5));
    }

    /**
     * Test NOT_LOWER operator (>=)
     */
    public function test_not_lower_operator()
    {
        $this->assertTrue($this->evaluateCondition('not_lower', 10, 5));
        $this->assertTrue($this->evaluateCondition('not_lower', 10, 10));
        $this->assertFalse($this->evaluateCondition('not_lower', 5, 10));
        
        // Mảng
        $this->assertFalse($this->evaluateCondition('not_lower', [1, 2], 5));
    }

    /**
     * Test IN operator với giá trị đơn
     */
    public function test_in_operator_single_value()
    {
        $this->assertTrue($this->evaluateCondition('in', 'apple', ['apple', 'banana', 'orange']));
        $this->assertFalse($this->evaluateCondition('in', 'grape', ['apple', 'banana', 'orange']));
        $this->assertTrue($this->evaluateCondition('in', 123, [123, 456, 789]));
        
        // Parameter không phải mảng
        $this->assertFalse($this->evaluateCondition('in', 'test', 'not_array'));
    }

    /**
     * Test IN operator với mảng
     */
    public function test_in_operator_array_value()
    {
        // Tất cả phần tử trong mảng đều nằm trong allowedValues
        $this->assertTrue($this->evaluateCondition('in', ['apple', 'banana'], ['apple', 'banana', 'orange']));
        
        // Một phần tử không nằm trong allowedValues
        $this->assertFalse($this->evaluateCondition('in', ['apple', 'grape'], ['apple', 'banana', 'orange']));
        
        // Mảng rỗng
        $this->assertTrue($this->evaluateCondition('in', [], ['apple', 'banana']));
    }

    /**
     * Test IN operator với parameter có cấu trúc [{value: ...}]
     */
    public function test_in_operator_structured_parameter()
    {
        $parameter = [
            ['value' => 'apple'],
            ['value' => 'banana'],
            ['value' => 'orange']
        ];

        $this->assertTrue($this->evaluateCondition('in', 'apple', $parameter));
        $this->assertFalse($this->evaluateCondition('in', 'grape', $parameter));
        $this->assertTrue($this->evaluateCondition('in', ['apple', 'banana'], $parameter));
    }

    /**
     * Test IN operator với parameter là string
     */
    public function test_in_operator_string_parameter()
    {
        // Parameter là string đơn
        $this->assertTrue($this->evaluateCondition('in', 'apple', 'apple'));
        $this->assertFalse($this->evaluateCondition('in', 'banana', 'apple'));

        // actualValue là mảng, parameter là string
        $this->assertTrue($this->evaluateCondition('in', ['apple'], 'apple'));
        $this->assertFalse($this->evaluateCondition('in', ['apple', 'banana'], 'apple')); // banana không có trong ['apple']

        // Parameter là số
        $this->assertTrue($this->evaluateCondition('in', 123, 123));
        $this->assertTrue($this->evaluateCondition('in', [123], 123));
        $this->assertFalse($this->evaluateCondition('in', 456, 123));
    }

    /**
     * Test NOT_IN operator
     */
    public function test_not_in_operator()
    {
        $this->assertFalse($this->evaluateCondition('not_in', 'apple', ['apple', 'banana', 'orange']));
        $this->assertTrue($this->evaluateCondition('not_in', 'grape', ['apple', 'banana', 'orange']));

        // Với mảng
        $this->assertFalse($this->evaluateCondition('not_in', ['apple', 'banana'], ['apple', 'banana', 'orange']));
        $this->assertTrue($this->evaluateCondition('not_in', ['grape', 'kiwi'], ['apple', 'banana', 'orange']));
        $this->assertFalse($this->evaluateCondition('not_in', ['apple', 'grape'], ['apple', 'banana', 'orange']));
    }

    /**
     * Test NOT_IN operator với parameter là string
     */
    public function test_not_in_operator_string_parameter()
    {
        // Parameter là string đơn
        $this->assertFalse($this->evaluateCondition('not_in', 'apple', 'apple'));
        $this->assertTrue($this->evaluateCondition('not_in', 'banana', 'apple'));

        // actualValue là mảng, parameter là string
        $this->assertFalse($this->evaluateCondition('not_in', ['apple'], 'apple'));
        $this->assertTrue($this->evaluateCondition('not_in', ['banana'], 'apple'));
        $this->assertFalse($this->evaluateCondition('not_in', ['apple', 'banana'], 'apple')); // apple có trong disallowed

        // Parameter là số
        $this->assertFalse($this->evaluateCondition('not_in', 123, 123));
        $this->assertTrue($this->evaluateCondition('not_in', 456, 123));
    }

    /**
     * Test CONTAIN operator với chuỗi
     */
    public function test_contain_operator_string()
    {
        $this->assertTrue($this->evaluateCondition('contain', 'Hello World', 'World'));
        $this->assertTrue($this->evaluateCondition('contain', 'Hello World', 'hello')); // case insensitive
        $this->assertFalse($this->evaluateCondition('contain', 'Hello World', 'xyz'));
        
        // Chuỗi rỗng
        $this->assertFalse($this->evaluateCondition('contain', '', 'test'));
        
        // Parameter không phải chuỗi
        $this->assertFalse($this->evaluateCondition('contain', 'test', 123));
    }

    /**
     * Test CONTAIN operator với mảng
     */
    public function test_contain_operator_array()
    {
        $this->assertTrue($this->evaluateCondition('contain', ['Hello World', 'Test'], 'World'));
        $this->assertTrue($this->evaluateCondition('contain', ['abc', 'Hello World'], 'hello')); // case insensitive
        $this->assertFalse($this->evaluateCondition('contain', ['abc', 'def'], 'xyz'));
        
        // Mảng có phần tử không phải chuỗi - chỉ kiểm tra phần tử chuỗi
        $this->assertTrue($this->evaluateCondition('contain', [123, 'test'], 'test')); // 'test' chứa 'test'
        $this->assertTrue($this->evaluateCondition('contain', ['test', 123], 'test')); // 'test' chứa 'test'
    }

    /**
     * Test NOT_CONTAIN operator
     */
    public function test_not_contain_operator()
    {
        $this->assertFalse($this->evaluateCondition('not_contain', 'Hello World', 'World'));
        $this->assertTrue($this->evaluateCondition('not_contain', 'Hello World', 'xyz'));
        
        // Với mảng
        $this->assertFalse($this->evaluateCondition('not_contain', ['Hello World', 'Test'], 'World'));
        $this->assertTrue($this->evaluateCondition('not_contain', ['abc', 'def'], 'xyz'));
        
        // Chuỗi rỗng
        $this->assertTrue($this->evaluateCondition('not_contain', '', 'test'));
    }

    /**
     * Test EMPTY operator
     */
    public function test_empty_operator()
    {
        // Mảng rỗng
        $this->assertTrue($this->evaluateCondition('empty', [], null));
        $this->assertFalse($this->evaluateCondition('empty', [1, 2, 3], null));

        // Chuỗi rỗng
        $this->assertTrue($this->evaluateCondition('empty', '', null));
        $this->assertTrue($this->evaluateCondition('empty', '   ', null)); // chỉ có space
        $this->assertFalse($this->evaluateCondition('empty', 'test', null));

        // Null
        $this->assertTrue($this->evaluateCondition('empty', null, null));

        // Số - không bao giờ empty
        $this->assertFalse($this->evaluateCondition('empty', 0, null));
        $this->assertFalse($this->evaluateCondition('empty', 123, null));
        $this->assertFalse($this->evaluateCondition('empty', -1, null));

        // Boolean
        $this->assertTrue($this->evaluateCondition('empty', false, null));
        $this->assertFalse($this->evaluateCondition('empty', true, null));
    }

    /**
     * Test NOT_EMPTY operator
     */
    public function test_not_empty_operator()
    {
        // Mảng
        $this->assertFalse($this->evaluateCondition('not_empty', [], null));
        $this->assertTrue($this->evaluateCondition('not_empty', [1, 2, 3], null));

        // Chuỗi
        $this->assertFalse($this->evaluateCondition('not_empty', '', null));
        $this->assertFalse($this->evaluateCondition('not_empty', '   ', null)); // chỉ có space
        $this->assertTrue($this->evaluateCondition('not_empty', 'test', null));

        // Null
        $this->assertFalse($this->evaluateCondition('not_empty', null, null));

        // Số - luôn not empty
        $this->assertTrue($this->evaluateCondition('not_empty', 0, null));
        $this->assertTrue($this->evaluateCondition('not_empty', 123, null));
        $this->assertTrue($this->evaluateCondition('not_empty', -1, null));
    }

    /**
     * Test BETWEEN operator với số
     */
    public function test_between_operator_numbers()
    {
        $this->assertTrue($this->evaluateCondition('between', 5, [1, 10]));
        $this->assertTrue($this->evaluateCondition('between', 1, [1, 10])); // boundary
        $this->assertTrue($this->evaluateCondition('between', 10, [1, 10])); // boundary
        $this->assertFalse($this->evaluateCondition('between', 0, [1, 10]));
        $this->assertFalse($this->evaluateCondition('between', 11, [1, 10]));

        // Thứ tự ngược
        $this->assertTrue($this->evaluateCondition('between', 5, [10, 1]));

        // Số thập phân
        $this->assertTrue($this->evaluateCondition('between', 5.5, [1.0, 10.0]));
    }

    /**
     * Test BETWEEN operator với ngày
     */
    public function test_between_operator_dates()
    {
        $this->assertTrue($this->evaluateCondition('between', '2024-01-15', ['2024-01-01', '2024-01-31']));
        $this->assertFalse($this->evaluateCondition('between', '2024-02-01', ['2024-01-01', '2024-01-31']));

        // Với thời gian
        $this->assertTrue($this->evaluateCondition('between', '2024-01-15 12:00:00', ['2024-01-15 10:00:00', '2024-01-15 14:00:00']));
    }

    /**
     * Test BETWEEN operator với chuỗi
     */
    public function test_between_operator_strings()
    {
        // Khi không parse được ngày, sẽ so sánh chuỗi
        $this->assertTrue($this->evaluateCondition('between', 'b', ['a', 'c']));
        $this->assertFalse($this->evaluateCondition('between', 'd', ['a', 'c']));
    }

    /**
     * Test BETWEEN operator với mảng và parameter không hợp lệ
     */
    public function test_between_operator_invalid_cases()
    {
        // Mảng không thể between
        $this->assertFalse($this->evaluateCondition('between', [1, 2, 3], [1, 10]));

        // Parameter không phải mảng
        $this->assertFalse($this->evaluateCondition('between', 5, 'not_array'));

        // Parameter không đủ 2 phần tử
        $this->assertFalse($this->evaluateCondition('between', 5, [1]));
        $this->assertFalse($this->evaluateCondition('between', 5, [1, 2, 3]));
    }

    /**
     * Test OTHER operator
     */
    public function test_other_operator()
    {
        // Giống EQUAL thì return false
        $this->assertFalse($this->evaluateCondition('other', 'test', 'test'));
        $this->assertTrue($this->evaluateCondition('other', 'test', 'TEST'));

        // So sánh số
        $this->assertFalse($this->evaluateCondition('other', 123, 123.0));
        $this->assertTrue($this->evaluateCondition('other', 123, 456));

        // So sánh ngày
        $this->assertFalse($this->evaluateCondition('other', '2024-01-01', '2024-01-01'));
        $this->assertTrue($this->evaluateCondition('other', '2024-01-01', '2024-01-02'));

        // Mảng với giá trị đơn
        $this->assertTrue($this->evaluateCondition('other', [1, 2, 3], 'test'));

        // Mảng với mảng
        $this->assertFalse($this->evaluateCondition('other', [1, 2, 3], [1, 2, 3]));
        $this->assertTrue($this->evaluateCondition('other', [1, 2, 3], [1, 2, 4]));

        // Parameter có cấu trúc đặc biệt
        $this->assertTrue($this->evaluateCondition('other', 'test', ['value' => 'different']));
        $this->assertFalse($this->evaluateCondition('other', 'test', ['value' => 'test']));
    }

    /**
     * Test với operator không hợp lệ
     */
    public function test_invalid_operator()
    {
        $this->assertFalse($this->evaluateCondition('invalid_operator', 'test', 'test'));
    }

    /**
     * Test với field không tồn tại
     */
    public function test_missing_field()
    {
        $condition = [
            'f' => 'non_existent_field',
            'o' => 'equal',
            'p' => 'test'
        ];

        $dataSource = ['other_field' => 'value'];

        $result = $this->service->evaluateSingleCondition($condition, $dataSource, 'form');
        $this->assertFalse($result); // null không bằng 'test'
    }

    /**
     * Test với condition thiếu thông tin
     */
    public function test_invalid_condition_structure()
    {
        // Thiếu field
        $condition = ['o' => 'equal', 'p' => 'test'];
        $this->assertFalse($this->service->evaluateSingleCondition($condition, [], 'form'));

        // Thiếu operator
        $condition = ['f' => 'test_field', 'p' => 'test'];
        $this->assertFalse($this->service->evaluateSingleCondition($condition, [], 'form'));
    }

    /**
     * Test với objectType không hợp lệ
     */
    public function test_invalid_object_type()
    {
        $condition = ['f' => 'test_field', 'o' => 'equal', 'p' => 'test'];
        $this->assertFalse($this->service->evaluateSingleCondition($condition, [], 'invalid_type'));
    }

    /**
     * Helper method để gọi evaluateSingleCondition
     */
    private function evaluateCondition(string $operator, $actualValue, $parameter): bool
    {
        $condition = [
            'f' => 'test_field',
            'o' => $operator,
            'p' => $parameter
        ];

        $dataSource = ['test_field' => $actualValue];

        return $this->service->evaluateSingleCondition($condition, $dataSource, 'form');
    }
}
