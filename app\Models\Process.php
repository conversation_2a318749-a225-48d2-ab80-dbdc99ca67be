<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Enums\ProcessVersionStatus;
use App\Models\Scopes\ActiveScope;
use App\Traits\HasTenantTrait;

class Process extends Model
{
    use HasUuids, HasTenantTrait;
    
    protected $table = 'process';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'description',
        'process_group_id',
        'status',
        'tenant_id',
        'create_by',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function processVersions()
    {
        return $this->hasMany('App\Models\ProcessVersion', 'process_id', 'id');
    }

    public function processVersionsWithoutActiveScope()
    {
        return $this->processVersions()->withoutGlobalScope(ActiveScope::class);
    }

    public function processVersionsWithoutScopes()
    {
        return $this->processVersions()->withoutGlobalScopes();
    }

    public function processVersionActive()
    {
        return $this->hasOne('App\Models\ProcessVersion', 'process_id', 'id')->where('is_active', ProcessVersionStatus::TRUE->value);
    }

    public function processGroup()
    {
        return $this->belongsTo(ProcessGroup::class, 'process_group_id', 'id');
    }
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'create_by', 'id');
    }
}
