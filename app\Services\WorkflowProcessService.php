<?php

namespace App\Services;

use App\Repositories\SaveJob\SaveJobRepositoryInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WorkflowProcessService
{
    protected $stageTransitionService;
    protected $emailService;
    protected $jobApprovalService;
    protected $saveJobRepository;

    public function __construct(
        StageTransitionService $stageTransitionService,
        EmailService $emailService,
        JobApprovalService $jobApprovalService,
        SaveJobRepositoryInterface $saveJobRepository
    ) {
        $this->stageTransitionService = $stageTransitionService;
        $this->emailService = $emailService;
        $this->jobApprovalService = $jobApprovalService;
        $this->saveJobRepository = $saveJobRepository;
    }

    /**
     * Xử lý quy trình
     */
    public function actionProcess($job_id, $fields, $processInstance, $stage_id, $action_id, $process_version, $comment = null, $pending_approval_id = null, $condition_ids = null, $email_condition_ids = null)
    {
        try {
            $save_job = $this->saveJobRepository->find($job_id);
            if (!$save_job) {
                Log::error("Job not found", ['job_id' => $job_id]);
                
                return false;
            }
            
            $field_values = $save_job->jobFieldValues
                ->pluck('field_value', 'field_id')
                ->mapWithKeys(function ($value, $key) use ($fields) {
                    return [$fields->where('id', $key)->value('keyword') => $value];
                })
                ->toArray();

            // Lấy user từ save_job
            $user = $save_job->user;
            
            // Xử lý Stage Transition và truyền user vào
            $fromStageIds = $this->stageTransitionService->getFromStageInStageTransitions(
                $field_values,
                $processInstance,
                $stage_id,
                $action_id,
                $process_version,
                $user,
                $condition_ids
            );
            // dd($fromStageIds);
            
            // Xử lý Email
            $emailStageIds = $this->emailService->getEmailInStage(
                $field_values,
                $processInstance,
                $stage_id,
                $action_id,
                $process_version,
                $user,
                $email_condition_ids,
            );
            // dd($emailStageIds);

            // Xử lý Sync Group + Xử lý gộp trong Stage Transition để lấy ra bước chuyển tiếp

            // Xử lý Process Transition + Xử lý gộp trong Stage Transition để lấy ra bước chuyển tiếp
            
            // Biến để theo dõi kết quả xử lý
            $approvalResult = true;
            $emailResult = true;
            
            // Xử lý lịch sử phê duyệt
            if (!empty($fromStageIds)) {
                $approvalResult = $this->processApprovalHistory(
                    $fromStageIds,
                    $save_job,
                    $stage_id,
                    $action_id,
                    $comment,
                    $pending_approval_id,
                    $job_id
                );

                if (!$approvalResult) {
                    return false;
                }
            }
            
            // Gửi email
            if (!empty($emailStageIds)) {
                $emailResult = $this->processEmailSending(
                    $field_values,
                    $fields,
                    $emailStageIds,
                    $job_id,
                    $fromStageIds
                );
                // Email thất bại không làm fail toàn bộ process
            }
            
            // Trả về true chỉ khi có ít nhất một action được thực hiện thành công
            $hasProcessedData = !empty($fromStageIds);
            
            if (!$hasProcessedData) {
                Log::warning("No data processed in actionProcess", [
                    'job_id' => $job_id,
                    'stage_id' => $stage_id,
                    'action_id' => $action_id
                ]);
                
                return false;
            }
            
            return $approvalResult;
            
        } catch (\Exception $e) {
            Log::error("ActionProcess failed", [
                'job_id' => $job_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Xử lý giá trị của field dựa trên type
     */
    public function processFieldValueByType($value, $type, $object_table = null, $column_table = null)
    {
        // Bỏ qua nếu không có giá trị
        if (empty($value)) {
            return $value;
        }
        
        switch ($type) {
            case 'USER':
                $user = app(\App\Models\User::class)->find($value);
                if ($user) {
                    return $user->full_name;
                }
                break;
                
            case 'DEPARTMENT':
                $department = app(\App\Models\Department::class)->find($value);
                if ($department) {
                    return $department->name;
                }
                break;
                
            case 'OBJECTSYSTEM':
                if ($object_table && $column_table) {
                    // Xác định namespace model từ tên bảng
                    $modelName = Str::studly(Str::singular($object_table));
                    $fullModelName = "\\App\\Models\\{$modelName}";
                    
                    // Kiểm tra model tồn tại
                    if (class_exists($fullModelName)) {
                        $object = $fullModelName::find($value);
                        if ($object && isset($object->{$column_table})) {
                            return $object->{$column_table};
                        }
                    } else {
                        // Fallback nếu model không tồn tại
                        $object = DB::table($object_table)->find($value);
                        if ($object && isset($object->{$column_table})) {
                            return $object->{$column_table};
                        }
                    }
                }
                break;
        }
        
        // Trả về giá trị gốc nếu không xử lý được
        return $value;
    }

    /**
     * Xử lý lịch sử phê duyệt
     */
    public function processApprovalHistory($fromStageIds, $save_job, $stage_id, $action_id, $comment, $pending_approval_id, $job_id)
    {
        try {
            $approvalResult = $this->jobApprovalService->saveJobApprovalHistory(
                $fromStageIds,
                $save_job,
                $stage_id,
                $action_id,
                $comment,
                $pending_approval_id
            );

            if (!$approvalResult) {
                Log::error("Failed to save job approval history", [
                    'job_id' => $job_id,
                    'fromStageIds' => $fromStageIds
                ]);

                return false;
            }

            return true;

        } catch (\Exception $e) {
            Log::error("ProcessApprovalHistory failed", [
                'job_id' => $job_id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Xử lý gửi email với field processing
     */
    public function processEmailSending($field_values, $fields, $emailStageIds, $job_id, $stageIds)
    {
        try {
            // Tạo bản sao của $field_values để xử lý
            $processed_field_values = $field_values;

            // Lấy thông tin chi tiết cho mỗi field dựa vào type
            foreach ($processed_field_values as $keyword => &$value) {
                // Bỏ qua nếu không có giá trị
                if (empty($value)) {
                    continue;
                }

                // Tìm thông tin field
                $field = $fields->firstWhere('keyword', $keyword);
                if (!$field) {
                    continue;
                }

                $type = $field->type ?? null;
                $object_table = $field->object_table ?? null;
                $column_table = $field->column_table ?? null;

                // Xử lý dựa vào kiểu dữ liệu của $value
                if (is_array($value)) {
                    // Xử lý từng phần tử trong mảng
                    $processedArray = [];
                    foreach ($value as $itemKey => $itemValue) {
                        $processedArray[$itemKey] = $this->processFieldValueByType($itemValue, $type, $object_table, $column_table);
                    }
                    $value = $processedArray;
                } else {
                    // Xử lý giá trị đơn
                    $value = $this->processFieldValueByType($value, $type, $object_table, $column_table);
                }
            }

            // Xóa biến tham chiếu sau vòng lặp
            unset($value);

            $emailResult = $this->emailService->sendEmail($processed_field_values, $emailStageIds, $job_id, $stageIds);

            if (!$emailResult) {
                Log::warning("Failed to send email", [
                    'job_id' => $job_id,
                    'emailStageIds' => $emailStageIds
                ]);
                // Email thất bại không làm fail toàn bộ process
                return false;
            }

            return true;

        } catch (\Exception $e) {
            Log::error("Email processing failed", [
                'job_id' => $job_id,
                'error' => $e->getMessage()
            ]);
            // Email thất bại không làm fail toàn bộ process
            return false;
        }
    }
}